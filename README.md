# 实体匹配与去重系统

一个专业的Web版实体匹配与去重解决方案，专门用于处理HW1作业中的数据匹配任务。

## 🚀 快速开始

### 1. 系统要求
- Python 3.8+
- 必需的Python包：flask, pandas, numpy, openpyxl, scikit-learn

### 2. 安装依赖
```bash
pip install flask pandas numpy openpyxl scikit-learn
```

### 3. 启动系统
```bash
python app.py
```

### 4. 访问Web界面
打开浏览器访问：http://127.0.0.1:5000

## 📁 项目结构

```
HW1/
├── app.py                 # Flask主应用
├── modules/               # 核心模块
│   ├── data_loader.py     # 数据加载模块
│   ├── preprocessor.py    # 文本预处理模块
│   ├── similarity_calculator.py  # 相似度计算模块
│   ├── matcher.py         # 实体匹配模块
│   ├── deduplicator.py    # 去重模块
│   └── evaluator.py       # 性能评估模块
├── templates/             # HTML模板
│   ├── base.html          # 基础模板
│   ├── index.html         # 首页
│   ├── load_data.html     # 数据加载页面
│   ├── deduplication.html # 去重处理页面
│   ├── matching.html      # 实体匹配页面
│   └── evaluation.html    # 性能评估页面
├── DATA/                  # 数据文件目录
│   ├── primary.csv        # 标准实体数据
│   ├── alternate.csv      # 实体变体数据
│   ├── test_01.csv        # 测试匹配数据
│   └── test_02.xlsx       # 变换测试数据
├── test_system.py         # 系统测试脚本
├── 开发文档.html          # 详细开发文档
└── README.md              # 本文件
```

## 🔧 核心功能

### 1. 数据加载与验证
- 自动加载primary.csv、alternate.csv、test_01.csv、test_02.xlsx
- 数据完整性验证
- 数据统计信息展示

### 2. 智能去重处理
- Primary数据内部去重
- Alternate与Primary数据交叉去重
- 多种相似度算法支持
- 可调节的相似度阈值

### 3. 多策略实体匹配
- Test_01与Primary数据匹配
- Test_02各sheet分别匹配（8个不同的数据变换）
- 自适应匹配策略
- 负样本检测（Sheet8特殊处理）

### 4. 性能评估分析
- Precision（精确率）
- Recall（召回率）
- F1-Score（综合指标）
- Accuracy（准确率）
- 负样本特异性评估

### 5. 结果导出
- CSV格式匹配结果
- XLSX格式评估报告
- ZIP打包批量下载

## 📊 数据集说明

| 数据文件 | 记录数 | 用途 | 说明 |
|---------|--------|------|------|
| primary.csv | 16,043 | 标准实体数据 | 作为匹配的目标数据集 |
| alternate.csv | 19,527 | 实体变体数据 | 需要与primary去重 |
| test_01.csv | 16,043 | 测试匹配数据 | 包含拼写错误和变体 |
| test_02.xlsx | 8×1,000 | 变换测试数据 | 8种不同的数据变换 |

### Test_02 Sheet说明

| Sheet | 数据变换类型 | 说明 |
|-------|-------------|------|
| Sheet1 | 移除特殊字符和空格 | remove all special characters and white spaces |
| Sheet2 | 单词打乱+移除特殊字符 | shuffle words then remove special characters |
| Sheet3 | 字符间插入空格 | separate every character by white space |
| Sheet4 | 单词打乱+字符分离 | shuffle words then separate characters |
| Sheet5 | 单词移除 | word removal |
| Sheet6 | 单词截断 | word truncation |
| Sheet7 | 待分析变换 | unknown transformation |
| Sheet8 | 负样本 | **无真实匹配，匹配不到才是正常的** |

## 🧠 算法特性

### 相似度算法
- **Levenshtein距离**：字符编辑距离
- **Jaro-Winkler相似度**：前缀权重相似度
- **Jaccard系数**：集合相似度
- **LCS**：最长公共子序列
- **组合算法**：多算法融合

### 匹配策略
- 多层次匹配（精确→模糊→语义）
- 自适应阈值调整
- 负样本检测机制
- 文本预处理优化

## 📈 使用流程

### 1. 数据加载
1. 访问"数据加载"页面
2. 点击"开始加载数据"
3. 查看数据统计信息

### 2. 去重处理（可选）
1. 访问"去重处理"页面
2. 配置相似度阈值和算法
3. 执行去重处理
4. 查看去重统计

### 3. 实体匹配
1. 访问"实体匹配"页面
2. 配置匹配参数
3. 执行匹配处理
4. 查看匹配结果

### 4. 性能评估
1. 访问"性能评估"页面
2. 计算评估指标
3. 查看详细分析
4. 获取改进建议

### 5. 结果导出
1. 点击"导出结果"按钮
2. 下载ZIP文件
3. 包含所有CSV/XLSX结果文件

## 📋 输出文件

### 去重结果
- `primary_deduped.csv` - Primary去重结果
- `alternate_deduped.csv` - Alternate去重结果

### 匹配结果
- `test_01_matches.csv` - Test_01匹配结果
- `test_02_sheet1_matches.csv` - Sheet1匹配结果
- `test_02_sheet2_matches.csv` - Sheet2匹配结果
- ... (每个sheet一个文件)
- `test_02_sheet8_matches.csv` - Sheet8匹配结果

### 评估报告
- `evaluation_report.json` - 详细评估指标

## ⚠️ 重要说明

### Sheet8特殊处理
Sheet8是负样本测试集，**没有真实匹配**。算法如果在Sheet8上匹配不到任何结果，说明算法性能良好，能够正确识别非匹配情况。

### 性能优化建议
- 建议相似度阈值设置为0.7-0.8
- 使用组合算法获得最佳效果
- 对于不同sheet类型，系统会自动调整匹配策略

## 🔍 故障排除

### 常见问题
1. **数据文件不存在**：确保DATA目录下有所有必需文件
2. **内存不足**：处理大数据集时可能需要更多内存
3. **处理时间长**：匹配过程可能需要5-10分钟，请耐心等待

### 技术支持
如遇到问题，请检查：
1. Python版本是否为3.8+
2. 所有依赖包是否正确安装
3. 数据文件格式是否正确
4. 系统内存是否充足

## 📞 联系信息

如需技术支持或有任何疑问，请及时沟通。

---

**实体匹配与去重系统** © 2024 | 基于Flask + Python开发
