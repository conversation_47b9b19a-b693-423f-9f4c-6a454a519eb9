#!/usr/bin/env python3
"""
分析Sheet7的数据变换特性
"""

import pandas as pd

def analyze_sheet7():
    # 加载数据
    primary = pd.read_csv('DATA/primary.csv')
    sheet7 = pd.read_excel('DATA/test_02.xlsx', sheet_name='Sheet7')
    
    print("=== Sheet7 数据变换分析 ===")
    print(f"Sheet7记录数: {len(sheet7)}")
    print(f"Primary记录数: {len(primary)}")
    
    print("\n=== 数据变换对比 ===")
    
    # 分析前5个记录
    for i in range(5):
        row = sheet7.iloc[i]
        id_val = row['ID']
        sheet7_name = row['NAME']
        
        # 查找对应的primary记录
        primary_match = primary[primary['ID'] == id_val]
        if len(primary_match) > 0:
            primary_name = primary_match['NAME'].values[0]
            print(f"ID {id_val}:")
            print(f"  Primary: {primary_name}")
            print(f"  Sheet7:  {sheet7_name}")
            print(f"  变换:    {primary_name} -> {sheet7_name}")
            print()
        else:
            print(f"ID {id_val}: 在Primary中未找到")
    
    print("=== Sheet7特点分析 ===")
    print("Sheet7是'initials'变换，特点:")
    print("1. 将完整姓名转换为首字母缩写")
    print("2. 保留姓氏的完整形式")
    print("3. 名字部分只保留首字母")
    print("4. 这种变换极大地减少了可匹配的信息")
    print("5. 我们的简单字符串匹配算法难以处理这种情况")

if __name__ == "__main__":
    analyze_sheet7()
