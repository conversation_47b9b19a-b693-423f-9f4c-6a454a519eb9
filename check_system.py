#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统状态检查脚本
System Status Check Script
"""

import os
import sys
import pandas as pd

def check_python_version():
    """检查Python版本"""
    print("🐍 Python版本检查")
    print(f"   当前版本: {sys.version}")
    
    version_info = sys.version_info
    if version_info.major >= 3 and version_info.minor >= 8:
        print("   ✅ Python版本符合要求 (3.8+)")
        return True
    else:
        print("   ❌ Python版本过低，需要3.8或更高版本")
        return False

def check_packages():
    """检查必需的Python包"""
    print("\n📦 Python包检查")
    
    required_packages = [
        'flask',
        'pandas', 
        'numpy',
        'openpyxl',
        'sklearn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n   需要安装的包: {', '.join(missing_packages)}")
        print(f"   安装命令: pip install {' '.join(missing_packages)}")
        return False
    else:
        print("   ✅ 所有必需包已安装")
        return True

def check_data_files():
    """检查数据文件"""
    print("\n📁 数据文件检查")
    
    required_files = [
        'DATA/primary.csv',
        'DATA/alternate.csv',
        'DATA/test_01.csv', 
        'DATA/test_02.xlsx'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            try:
                if file_path.endswith('.csv'):
                    df = pd.read_csv(file_path)
                    print(f"   ✅ {file_path} ({len(df)} 条记录)")
                elif file_path.endswith('.xlsx'):
                    xl = pd.ExcelFile(file_path)
                    print(f"   ✅ {file_path} ({len(xl.sheet_names)} 个sheet)")
            except Exception as e:
                print(f"   ⚠️ {file_path} (文件损坏: {str(e)})")
        else:
            print(f"   ❌ {file_path} (文件不存在)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n   缺失的文件: {', '.join(missing_files)}")
        return False
    else:
        print("   ✅ 所有数据文件存在且可读")
        return True

def check_directories():
    """检查目录结构"""
    print("\n📂 目录结构检查")
    
    required_dirs = [
        'modules',
        'templates',
        'DATA'
    ]
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            print(f"   ✅ {dir_name}/")
        else:
            print(f"   ❌ {dir_name}/ (目录不存在)")
            return False
    
    # 检查模块文件
    module_files = [
        'modules/__init__.py',
        'modules/data_loader.py',
        'modules/preprocessor.py',
        'modules/similarity_calculator.py',
        'modules/matcher.py',
        'modules/deduplicator.py',
        'modules/evaluator.py'
    ]
    
    for file_path in module_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            return False
    
    # 检查模板文件
    template_files = [
        'templates/base.html',
        'templates/index.html',
        'templates/load_data.html',
        'templates/deduplication.html',
        'templates/matching.html',
        'templates/evaluation.html'
    ]
    
    for file_path in template_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            return False
    
    print("   ✅ 目录结构完整")
    return True

def check_app_file():
    """检查主应用文件"""
    print("\n🚀 应用文件检查")
    
    if os.path.exists('app.py'):
        print("   ✅ app.py 存在")
        
        # 尝试导入检查语法
        try:
            import ast
            with open('app.py', 'r', encoding='utf-8') as f:
                content = f.read()
            ast.parse(content)
            print("   ✅ app.py 语法正确")
            return True
        except SyntaxError as e:
            print(f"   ❌ app.py 语法错误: {str(e)}")
            return False
        except Exception as e:
            print(f"   ⚠️ app.py 检查异常: {str(e)}")
            return True
    else:
        print("   ❌ app.py 不存在")
        return False

def main():
    """主检查函数"""
    print("🔍 实体匹配与去重系统 - 状态检查")
    print("=" * 60)
    
    checks = [
        check_python_version,
        check_packages,
        check_directories,
        check_app_file,
        check_data_files
    ]
    
    passed = 0
    total = len(checks)
    
    for check_func in checks:
        if check_func():
            passed += 1
        print()  # 空行分隔
    
    print("=" * 60)
    print(f"📊 检查结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 系统状态良好！可以正常使用。")
        print("\n🚀 启动系统:")
        print("   python app.py")
        print("\n🌐 访问地址:")
        print("   http://127.0.0.1:5000")
    else:
        print("⚠️ 系统存在问题，请根据上述检查结果进行修复。")
        
        if passed >= total - 1:
            print("\n💡 提示: 大部分组件正常，可以尝试启动系统。")
    
    return passed == total

if __name__ == "__main__":
    main()
