#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
去重模块
Deduplication Module
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
import logging
from .preprocessor import TextPreprocessor
from .similarity_calculator import SimilarityCalculator
from collections import defaultdict
import time

logger = logging.getLogger(__name__)

class EntityDeduplicator:
    """实体去重器类"""
    
    def __init__(self):
        self.preprocessor = TextPreprocessor()
        self.similarity_calc = SimilarityCalculator()
    
    def deduplicate_primary(self,
                          primary_df: pd.DataFrame,
                          threshold: float = 0.8,
                          method: str = 'combined',
                          progress_callback=None) -> pd.DataFrame:
        """
        对primary数据进行去重 - 超高速版本

        Args:
            primary_df: primary数据集
            threshold: 相似度阈值
            method: 相似度计算方法

        Returns:
            pd.DataFrame: 去重后的数据
        """
        logger.info(f"开始primary数据去重，原始数据: {len(primary_df)} 条")
        start_time = time.time()

        # 使用超高速pandas去重
        return self._ultra_fast_deduplicate(primary_df, threshold, method, progress_callback)

    def _ultra_fast_deduplicate(self, df: pd.DataFrame, threshold: float, method: str, progress_callback=None) -> pd.DataFrame:
        """
        超高速去重 - 使用pandas内置方法，秒级完成
        """
        logger.info("使用超高速pandas去重...")

        if progress_callback:
            progress_callback(10)

        # 第一步：完全相同名称去重
        logger.info("第一步：完全相同名称去重...")
        df_clean = df.copy()
        df_clean['name_upper'] = df_clean['NAME'].str.upper().str.strip()

        # 保留每个名称的第一个出现（ID最小的）
        df_clean = df_clean.sort_values('ID').drop_duplicates(subset=['name_upper'], keep='first')

        if progress_callback:
            progress_callback(40)

        # 第二步：相同ID去重（保留名称最长的）
        logger.info("第二步：相同ID去重...")
        df_clean['name_length'] = df_clean['NAME'].str.len()
        df_clean = df_clean.sort_values(['ID', 'name_length'], ascending=[True, False])
        df_clean = df_clean.drop_duplicates(subset=['ID'], keep='first')

        if progress_callback:
            progress_callback(70)

        # 第三步：基于名称前缀的快速去重
        logger.info("第三步：名称前缀去重...")
        df_clean['name_prefix'] = df_clean['name_upper'].str[:8]
        df_clean = df_clean.sort_values(['ID', 'name_length'], ascending=[True, False])
        df_clean = df_clean.drop_duplicates(subset=['ID', 'name_prefix'], keep='first')

        if progress_callback:
            progress_callback(90)

        # 清理临时列
        result_df = df_clean.drop(['name_upper', 'name_length', 'name_prefix'], axis=1)

        if progress_callback:
            progress_callback(100)

        logger.info(f"超高速去重完成，原始数据: {len(df)} 条，去重后: {len(result_df)} 条，移除: {len(df) - len(result_df)} 条")

        return result_df

    def _fast_deduplicate(self, df: pd.DataFrame, threshold: float, method: str, progress_callback=None) -> pd.DataFrame:
        """
        高性能去重算法
        使用分块处理和预过滤来提高性能
        """
        # 复制数据
        data = df.copy().reset_index(drop=True)
        total_rows = len(data)

        # 预处理所有名称
        logger.info("预处理文本数据...")
        data['processed_name'] = data['NAME'].apply(lambda x: self.preprocessor.normalize_text(str(x)))

        # 按名称长度分组，减少比较次数
        logger.info("按名称长度分组...")
        length_groups = defaultdict(list)
        for idx, row in data.iterrows():
            name_len = len(row['processed_name'])
            length_groups[name_len].append(idx)

        # 标记重复项
        duplicates_to_remove = set()
        processed = set()

        # 分块处理
        chunk_size = min(1000, total_rows // 10 + 1)
        processed_count = 0

        logger.info(f"开始分块去重，块大小: {chunk_size}")

        # 对每个长度组进行处理
        for name_len, indices in length_groups.items():
            if len(indices) <= 1:
                processed_count += len(indices)
                continue

            # 处理当前长度组
            group_duplicates = self._process_length_group(data, indices, threshold, method)
            duplicates_to_remove.update(group_duplicates)
            processed_count += len(indices)

            # 更新进度
            if progress_callback:
                progress = min(int((processed_count / total_rows) * 90), 90)
                progress_callback(progress)

        # 跨长度组检查（暂时跳过以提高速度）
        logger.info("跳过跨长度组去重检查以提高速度...")
        # cross_group_duplicates = self._fast_cross_group_deduplication(data, length_groups, threshold, method, duplicates_to_remove)
        # duplicates_to_remove.update(cross_group_duplicates)

        # 移除重复项
        if progress_callback:
            progress_callback(95)

        deduped_df = data.drop(duplicates_to_remove).copy()
        deduped_df = deduped_df.drop(['processed_name'], axis=1)

        if progress_callback:
            progress_callback(100)

        logger.info(f"Primary去重完成，去重后数据: {len(deduped_df)} 条，移除: {len(duplicates_to_remove)} 条")

        return deduped_df

    def _sql_deduplicate(self, df: pd.DataFrame, threshold: float, method: str, progress_callback=None) -> pd.DataFrame:
        """
        超高速SQL风格去重 - 秒级完成
        """
        import sqlite3
        import tempfile
        import os

        logger.info("使用SQL引擎进行超高速去重...")

        # 创建临时数据库
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()

        try:
            conn = sqlite3.connect(temp_db.name)

            # 将数据写入SQLite
            if progress_callback:
                progress_callback(10)

            data_copy = df.copy().reset_index(drop=True)
            data_copy['original_index'] = data_copy.index
            data_copy.to_sql('records', conn, index=False, if_exists='replace')

            if progress_callback:
                progress_callback(20)

            # 创建索引加速查询
            conn.execute("CREATE INDEX idx_name ON records(NAME)")
            conn.execute("CREATE INDEX idx_id ON records(ID)")

            if progress_callback:
                progress_callback(30)

            # SQL去重策略：多层快速去重
            logger.info("执行SQL去重查询...")

            # 第一步：完全相同的名称去重（保留ID最小的）
            conn.execute("""
                DELETE FROM records
                WHERE rowid NOT IN (
                    SELECT MIN(rowid)
                    FROM records
                    GROUP BY UPPER(TRIM(NAME))
                )
            """)

            if progress_callback:
                progress_callback(50)

            # 第二步：基于ID的去重（同ID保留名称最长的）
            conn.execute("""
                DELETE FROM records
                WHERE rowid NOT IN (
                    SELECT rowid FROM (
                        SELECT rowid,
                               ROW_NUMBER() OVER (
                                   PARTITION BY ID
                                   ORDER BY LENGTH(NAME) DESC, NAME
                               ) as rn
                        FROM records
                    ) WHERE rn = 1
                )
            """)

            if progress_callback:
                progress_callback(70)

            # 第三步：基于名称前缀的快速去重
            conn.execute("""
                DELETE FROM records
                WHERE rowid NOT IN (
                    SELECT MIN(rowid)
                    FROM records
                    GROUP BY ID, SUBSTR(UPPER(TRIM(NAME)), 1, 8)
                )
            """)

            if progress_callback:
                progress_callback(90)

            # 获取去重后的数据
            result_df = pd.read_sql("SELECT * FROM records ORDER BY original_index", conn)
            result_df = result_df.drop('original_index', axis=1)

            conn.close()

            if progress_callback:
                progress_callback(100)

            logger.info(f"SQL去重完成，原始数据: {len(df)} 条，去重后: {len(result_df)} 条，移除: {len(df) - len(result_df)} 条")

            return result_df

        finally:
            # 清理临时文件
            try:
                os.unlink(temp_db.name)
            except:
                pass

    def _process_length_group(self, data: pd.DataFrame, indices: list, threshold: float, method: str) -> set:
        """
        处理同一长度组内的去重
        """
        duplicates = set()
        processed = set()

        for i, idx1 in enumerate(indices):
            if idx1 in processed:
                continue

            current_group = [idx1]
            name1 = data.loc[idx1, 'processed_name']

            # 只与后续未处理的记录比较
            for idx2 in indices[i+1:]:
                if idx2 in processed:
                    continue

                name2 = data.loc[idx2, 'processed_name']

                # 快速预过滤：如果长度差异太大，跳过
                if abs(len(name1) - len(name2)) > len(name1) * (1 - threshold):
                    continue

                similarity = self._calculate_similarity_fast(name1, name2, method)

                if similarity >= threshold:
                    current_group.append(idx2)
                    processed.add(idx2)

            # 如果找到重复项，保留最佳记录
            if len(current_group) > 1:
                best_idx = self._select_best_record_fast(data.loc[current_group])
                for dup_idx in current_group:
                    if dup_idx != best_idx:
                        duplicates.add(dup_idx)

            processed.add(idx1)

        return duplicates

    def _fast_cross_group_deduplication(self, data: pd.DataFrame, length_groups: dict, threshold: float, method: str, existing_duplicates: set) -> set:
        """
        快速跨长度组去重检查 - 大幅简化版本
        """
        duplicates = set()

        # 只检查相邻长度组，且大幅限制比较数量
        sorted_lengths = sorted(length_groups.keys())

        for i, len1 in enumerate(sorted_lengths):
            if i + 1 >= len(sorted_lengths):
                break

            len2 = sorted_lengths[i + 1]
            if abs(len1 - len2) > 1:  # 只检查长度差为1的组
                continue

            indices1 = [idx for idx in length_groups[len1] if idx not in existing_duplicates]
            indices2 = [idx for idx in length_groups[len2] if idx not in existing_duplicates]

            if not indices1 or not indices2:
                continue

            # 大幅限制比较数量 - 只比较前10个
            max_compare = min(10, len(indices1), len(indices2))

            # 比较两个组之间的记录（限制数量）
            group_duplicates = self._compare_groups_limited(data, indices1[:max_compare], indices2[:max_compare], threshold, method)
            duplicates.update(group_duplicates)

        return duplicates

    def _compare_groups_limited(self, data: pd.DataFrame, group1: list, group2: list, threshold: float, method: str) -> set:
        """
        比较两个组之间的记录 - 限制版本
        """
        duplicates = set()

        for idx1 in group1:
            name1 = data.loc[idx1, 'processed_name']

            for idx2 in group2:
                name2 = data.loc[idx2, 'processed_name']

                similarity = self._calculate_similarity_fast(name1, name2, method)

                if similarity >= threshold:
                    # 选择保留哪个记录
                    record1 = data.loc[[idx1]]
                    record2 = data.loc[[idx2]]
                    combined = pd.concat([record1, record2])
                    best_idx = self._select_best_record_fast(combined)

                    if best_idx == idx1:
                        duplicates.add(idx2)
                    else:
                        duplicates.add(idx1)
                    break  # 找到匹配后跳出内层循环

        return duplicates

    def _calculate_similarity_fast(self, text1: str, text2: str, method: str) -> float:
        """
        快速相似度计算（文本已预处理）
        """
        if method == 'levenshtein':
            return self.similarity_calc.levenshtein_similarity(text1, text2)
        elif method == 'jaro_winkler':
            return self.similarity_calc.jaro_winkler_similarity(text1, text2)
        elif method == 'jaccard':
            return self.similarity_calc.jaccard_token_similarity(text1, text2)
        elif method == 'combined':
            return self.similarity_calc.combined_similarity(text1, text2)
        else:
            return self.similarity_calc.combined_similarity(text1, text2)

    def _select_best_record_fast(self, group_df: pd.DataFrame) -> int:
        """
        快速选择最佳记录
        """
        if len(group_df) == 1:
            return group_df.index[0]

        # 选择策略：名称最长的记录，如果长度相同选择ID最小的
        best_idx = None
        max_length = 0
        min_id = float('inf')

        for idx, row in group_df.iterrows():
            name_length = len(str(row['NAME']))
            current_id = row['ID']

            if (name_length > max_length or
                (name_length == max_length and current_id < min_id)):
                max_length = name_length
                min_id = current_id
                best_idx = idx

        return best_idx
    
    def deduplicate_alternate(self,
                            alternate_df: pd.DataFrame,
                            primary_df: pd.DataFrame,
                            threshold: float = 0.8,
                            method: str = 'combined',
                            progress_callback=None) -> pd.DataFrame:
        """
        对alternate数据进行去重（与primary数据比较）- 高性能版本

        Args:
            alternate_df: alternate数据集
            primary_df: primary数据集
            threshold: 相似度阈值
            method: 相似度计算方法

        Returns:
            pd.DataFrame: 去重后的数据
        """
        logger.info(f"开始alternate数据去重，原始数据: {len(alternate_df)} 条")

        # 使用超高速pandas方法
        return self._ultra_fast_deduplicate_alternate(alternate_df, primary_df, threshold, method, progress_callback)

    def _fast_deduplicate_alternate(self, alternate_df: pd.DataFrame, primary_df: pd.DataFrame,
                                  threshold: float, method: str, progress_callback=None) -> pd.DataFrame:
        """
        高性能alternate去重
        """
        # 复制数据
        alt_data = alternate_df.copy().reset_index(drop=True)

        # 预处理primary数据，建立索引
        logger.info("建立primary数据索引...")
        primary_index = self._build_primary_index(primary_df, method)

        # 标记重复项
        alt_data['is_duplicate'] = False
        alt_data['matched_primary_id'] = None
        alt_data['similarity_score'] = 0.0

        total_rows = len(alt_data)

        # 批量处理alternate数据
        logger.info("批量匹配alternate数据...")
        batch_size = min(500, total_rows // 10 + 1)

        for i in range(0, total_rows, batch_size):
            end_idx = min(i + batch_size, total_rows)
            batch = alt_data.iloc[i:end_idx]

            # 处理当前批次
            self._process_alternate_batch(batch, primary_index, threshold, method, alt_data)

            # 更新进度
            if progress_callback:
                progress = min(int((end_idx / total_rows) * 70), 70)
                progress_callback(progress)

        # 内部去重（alternate数据内部的重复）
        logger.info("alternate内部去重...")
        if progress_callback:
            progress_callback(75)

        non_duplicate_data = alt_data[~alt_data['is_duplicate']].copy()
        if len(non_duplicate_data) > 1:
            internal_duplicates = self._fast_internal_deduplication(non_duplicate_data, threshold, method)
            for idx in internal_duplicates:
                alt_data.loc[idx, 'is_duplicate'] = True

        # 移除重复项
        if progress_callback:
            progress_callback(95)

        deduped_df = alt_data[~alt_data['is_duplicate']].copy()
        deduped_df = deduped_df.drop(['is_duplicate', 'matched_primary_id', 'similarity_score'], axis=1)

        if progress_callback:
            progress_callback(100)

        logger.info(f"Alternate去重完成，去重后数据: {len(deduped_df)} 条，移除: {len(alt_data) - len(deduped_df)} 条")

        return deduped_df

    def _ultra_fast_deduplicate_alternate(self, alternate_df: pd.DataFrame, primary_df: pd.DataFrame,
                                        threshold: float, method: str, progress_callback=None) -> pd.DataFrame:
        """
        超高速alternate去重 - 使用pandas内置方法
        """
        logger.info("使用超高速pandas进行alternate去重...")

        if progress_callback:
            progress_callback(10)

        # 准备数据
        alt_clean = alternate_df.copy()
        primary_clean = primary_df.copy()

        # 标准化名称
        alt_clean['name_upper'] = alt_clean['NAME'].str.upper().str.strip()
        primary_clean['name_upper'] = primary_clean['NAME'].str.upper().str.strip()

        if progress_callback:
            progress_callback(25)

        # 第一步：删除与primary完全相同名称的记录
        logger.info("删除与primary相同名称的记录...")
        primary_names = set(primary_clean['name_upper'])
        alt_clean = alt_clean[~alt_clean['name_upper'].isin(primary_names)]

        if progress_callback:
            progress_callback(50)

        # 第二步：删除与primary相同ID的记录
        logger.info("删除与primary相同ID的记录...")
        primary_ids = set(primary_clean['ID'])
        alt_clean = alt_clean[~alt_clean['ID'].isin(primary_ids)]

        if progress_callback:
            progress_callback(75)

        # 第三步：alternate内部去重
        logger.info("alternate内部去重...")
        alt_clean = alt_clean.sort_values('ID').drop_duplicates(subset=['name_upper'], keep='first')

        if progress_callback:
            progress_callback(90)

        # 清理临时列
        result_df = alt_clean.drop(['name_upper'], axis=1)

        if progress_callback:
            progress_callback(100)

        logger.info(f"Alternate超高速去重完成，原始数据: {len(alternate_df)} 条，去重后: {len(result_df)} 条，移除: {len(alternate_df) - len(result_df)} 条")

        return result_df

    def _sql_deduplicate_alternate(self, alternate_df: pd.DataFrame, primary_df: pd.DataFrame,
                                 threshold: float, method: str, progress_callback=None) -> pd.DataFrame:
        """
        超高速SQL风格alternate去重
        """
        import sqlite3
        import tempfile
        import os

        logger.info("使用SQL引擎进行alternate超高速去重...")

        # 创建临时数据库
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()

        try:
            conn = sqlite3.connect(temp_db.name)

            # 将数据写入SQLite
            if progress_callback:
                progress_callback(10)

            # 写入primary数据
            primary_copy = primary_df.copy()
            primary_copy.to_sql('primary_records', conn, index=False, if_exists='replace')

            # 写入alternate数据
            alternate_copy = alternate_df.copy()
            alternate_copy['original_index'] = alternate_copy.index
            alternate_copy.to_sql('alternate_records', conn, index=False, if_exists='replace')

            if progress_callback:
                progress_callback(25)

            # 创建索引
            conn.execute("CREATE INDEX idx_primary_name ON primary_records(NAME)")
            conn.execute("CREATE INDEX idx_primary_id ON primary_records(ID)")
            conn.execute("CREATE INDEX idx_alternate_name ON alternate_records(NAME)")
            conn.execute("CREATE INDEX idx_alternate_id ON alternate_records(ID)")

            if progress_callback:
                progress_callback(40)

            # SQL去重策略
            logger.info("执行SQL去重查询...")

            # 第一步：删除与primary完全相同名称的记录
            conn.execute("""
                DELETE FROM alternate_records
                WHERE UPPER(TRIM(NAME)) IN (
                    SELECT UPPER(TRIM(NAME)) FROM primary_records
                )
            """)

            if progress_callback:
                progress_callback(60)

            # 第二步：删除与primary相同ID的记录
            conn.execute("""
                DELETE FROM alternate_records
                WHERE ID IN (
                    SELECT ID FROM primary_records
                )
            """)

            if progress_callback:
                progress_callback(75)

            # 第三步：alternate内部去重
            conn.execute("""
                DELETE FROM alternate_records
                WHERE rowid NOT IN (
                    SELECT MIN(rowid)
                    FROM alternate_records
                    GROUP BY UPPER(TRIM(NAME))
                )
            """)

            if progress_callback:
                progress_callback(90)

            # 获取去重后的数据
            result_df = pd.read_sql("SELECT * FROM alternate_records ORDER BY original_index", conn)
            result_df = result_df.drop('original_index', axis=1)

            conn.close()

            if progress_callback:
                progress_callback(100)

            logger.info(f"Alternate SQL去重完成，原始数据: {len(alternate_df)} 条，去重后: {len(result_df)} 条，移除: {len(alternate_df) - len(result_df)} 条")

            return result_df

        finally:
            # 清理临时文件
            try:
                os.unlink(temp_db.name)
            except:
                pass

    def _build_primary_index(self, primary_df: pd.DataFrame, method: str) -> dict:
        """
        建立primary数据的索引以加速查找
        """
        index = {
            'by_id': {},
            'by_name_length': defaultdict(list),
            'processed_names': {}
        }

        for idx, row in primary_df.iterrows():
            entity_id = row['ID']
            name = str(row['NAME'])
            processed_name = self.preprocessor.normalize_text(name)

            # ID索引
            if entity_id not in index['by_id']:
                index['by_id'][entity_id] = []
            index['by_id'][entity_id].append({
                'idx': idx,
                'name': name,
                'processed_name': processed_name
            })

            # 按名称长度索引
            name_len = len(processed_name)
            index['by_name_length'][name_len].append({
                'idx': idx,
                'id': entity_id,
                'name': name,
                'processed_name': processed_name
            })

            # 预处理名称缓存
            index['processed_names'][idx] = processed_name

        return index

    def _process_alternate_batch(self, batch: pd.DataFrame, primary_index: dict,
                               threshold: float, method: str, alt_data: pd.DataFrame):
        """
        处理alternate数据批次
        """
        for idx, row in batch.iterrows():
            alt_name = str(row['NAME'])
            alt_id = row['ID']

            best_match = self._find_primary_match_fast(alt_name, alt_id, primary_index, threshold, method)

            if best_match is not None:
                alt_data.loc[idx, 'is_duplicate'] = True
                alt_data.loc[idx, 'matched_primary_id'] = best_match['id']
                alt_data.loc[idx, 'similarity_score'] = best_match['score']

    def _find_primary_match_fast(self, alt_name: str, alt_id: int, primary_index: dict,
                               threshold: float, method: str) -> Dict:
        """
        快速查找primary匹配
        """
        processed_alt_name = self.preprocessor.normalize_text(alt_name)
        best_score = 0.0
        best_match = None

        # 首先检查ID匹配
        if alt_id in primary_index['by_id']:
            for primary_record in primary_index['by_id'][alt_id]:
                similarity = self._calculate_similarity_fast(processed_alt_name,
                                                           primary_record['processed_name'], method)
                if similarity >= threshold * 0.7:  # ID匹配降低阈值
                    return {
                        'id': alt_id,
                        'name': primary_record['name'],
                        'score': similarity,
                        'match_type': 'id_and_name'
                    }

        # 名称匹配 - 只检查长度相近的记录
        alt_name_len = len(processed_alt_name)
        search_range = max(1, int(alt_name_len * 0.3))  # 搜索范围

        for name_len in range(max(1, alt_name_len - search_range),
                             alt_name_len + search_range + 1):
            if name_len not in primary_index['by_name_length']:
                continue

            for primary_record in primary_index['by_name_length'][name_len]:
                similarity = self._calculate_similarity_fast(processed_alt_name,
                                                           primary_record['processed_name'], method)

                if similarity > best_score and similarity >= threshold:
                    best_score = similarity
                    best_match = {
                        'id': primary_record['id'],
                        'name': primary_record['name'],
                        'score': similarity,
                        'match_type': 'name_only'
                    }

        return best_match

    def _fast_internal_deduplication(self, data: pd.DataFrame, threshold: float, method: str) -> set:
        """
        快速内部去重
        """
        duplicates = set()

        # 预处理名称
        data = data.copy()
        data['processed_name'] = data['NAME'].apply(lambda x: self.preprocessor.normalize_text(str(x)))

        # 按长度分组
        length_groups = defaultdict(list)
        for idx, row in data.iterrows():
            name_len = len(row['processed_name'])
            length_groups[name_len].append(idx)

        # 处理每个长度组
        for name_len, indices in length_groups.items():
            if len(indices) <= 1:
                continue
            group_duplicates = self._process_length_group(data, indices, threshold, method)
            duplicates.update(group_duplicates)

        return duplicates
    
    def _calculate_similarity(self, text1: str, text2: str, method: str) -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1, text2: 两个文本
            method: 相似度计算方法
            
        Returns:
            float: 相似度分数
        """
        # 预处理文本
        processed_text1 = self.preprocessor.normalize_text(text1)
        processed_text2 = self.preprocessor.normalize_text(text2)
        
        if method == 'levenshtein':
            return self.similarity_calc.levenshtein_similarity(processed_text1, processed_text2)
        elif method == 'jaro_winkler':
            return self.similarity_calc.jaro_winkler_similarity(processed_text1, processed_text2)
        elif method == 'jaccard':
            return self.similarity_calc.jaccard_token_similarity(processed_text1, processed_text2)
        elif method == 'combined':
            return self.similarity_calc.combined_similarity(processed_text1, processed_text2)
        else:
            return self.similarity_calc.combined_similarity(processed_text1, processed_text2)
    
    def _select_best_record(self, group_df: pd.DataFrame) -> int:
        """
        从重复组中选择最佳记录
        
        Args:
            group_df: 重复组数据
            
        Returns:
            int: 最佳记录的索引
        """
        # 选择策略：
        # 1. 名称最长的记录
        # 2. 如果长度相同，选择ID最小的
        
        best_idx = None
        max_length = 0
        min_id = float('inf')
        
        for idx, row in group_df.iterrows():
            name_length = len(str(row['NAME']))
            current_id = row['ID']
            
            if (name_length > max_length or 
                (name_length == max_length and current_id < min_id)):
                max_length = name_length
                min_id = current_id
                best_idx = idx
        
        return best_idx
    

    
    def get_deduplication_report(self, 
                               original_df: pd.DataFrame,
                               deduped_df: pd.DataFrame,
                               data_type: str) -> Dict:
        """
        生成去重报告
        
        Args:
            original_df: 原始数据
            deduped_df: 去重后数据
            data_type: 数据类型
            
        Returns:
            Dict: 去重报告
        """
        report = {
            'data_type': data_type,
            'original_count': len(original_df),
            'deduped_count': len(deduped_df),
            'removed_count': len(original_df) - len(deduped_df),
            'deduplication_rate': (len(original_df) - len(deduped_df)) / len(original_df) * 100,
            'unique_ids': {
                'original': original_df['ID'].nunique(),
                'deduped': deduped_df['ID'].nunique()
            }
        }
        
        return report
