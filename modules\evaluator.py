#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能评估模块
Performance Evaluation Module
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging

logger = logging.getLogger(__name__)

class PerformanceEvaluator:
    """性能评估器类"""
    
    def __init__(self):
        pass
    
    def evaluate_all_results(self, 
                           match_results: Dict[str, pd.DataFrame],
                           test_01_df: pd.DataFrame,
                           test_02_sheets: Dict[str, pd.DataFrame]) -> Dict:
        """
        评估所有匹配结果
        
        Args:
            match_results: 匹配结果
            test_01_df: test_01原始数据
            test_02_sheets: test_02各sheet数据
            
        Returns:
            Dict: 评估结果
        """
        logger.info("开始性能评估...")
        
        evaluation_results = {
            'test_01': {},
            'test_02': {},
            'summary': {}
        }
        
        # 评估test_01结果
        if 'test_01_matches' in match_results:
            evaluation_results['test_01'] = self.evaluate_test_01(
                match_results['test_01_matches'],
                test_01_df
            )
        
        # 评估test_02各sheet结果
        if 'test_02_matches' in match_results:
            for sheet_name, matches_df in match_results['test_02_matches'].items():
                if sheet_name in test_02_sheets:
                    evaluation_results['test_02'][sheet_name] = self.evaluate_test_02_sheet(
                        matches_df,
                        test_02_sheets[sheet_name],
                        sheet_name
                    )
        
        # 生成总结
        evaluation_results['summary'] = self.generate_summary(evaluation_results)
        
        logger.info("性能评估完成")
        return evaluation_results
    
    def evaluate_test_01(self, 
                        matches_df: pd.DataFrame,
                        original_df: pd.DataFrame) -> Dict:
        """
        评估test_01的匹配结果
        
        Args:
            matches_df: 匹配结果
            original_df: 原始test_01数据
            
        Returns:
            Dict: 评估指标
        """
        total_queries = len(original_df)
        total_matches = len(matches_df)
        
        # 计算真正例（TP）- 匹配的ID相同
        true_positives = 0
        false_positives = 0
        
        for _, match_row in matches_df.iterrows():
            query_id = match_row['query_id']
            matched_id = match_row['matched_id']
            
            if query_id == matched_id:
                true_positives += 1
            else:
                false_positives += 1
        
        false_negatives = total_queries - true_positives
        true_negatives = 0  # 在这个场景中，TN不太适用
        
        # 计算指标
        precision = true_positives / total_matches if total_matches > 0 else 0.0
        recall = true_positives / total_queries if total_queries > 0 else 0.0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
        accuracy = true_positives / total_queries if total_queries > 0 else 0.0
        
        return {
            'total_queries': total_queries,
            'total_matches': total_matches,
            'true_positives': true_positives,
            'false_positives': false_positives,
            'false_negatives': false_negatives,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'accuracy': accuracy,
            'match_rate': total_matches / total_queries if total_queries > 0 else 0.0,
            'avg_similarity': matches_df['similarity_score'].mean() if len(matches_df) > 0 else 0.0,
            'similarity_distribution': self._get_similarity_distribution(matches_df)
        }
    
    def evaluate_test_02_sheet(self, 
                             matches_df: pd.DataFrame,
                             original_df: pd.DataFrame,
                             sheet_name: str) -> Dict:
        """
        评估test_02单个sheet的匹配结果
        
        Args:
            matches_df: 匹配结果
            original_df: 原始sheet数据
            sheet_name: sheet名称
            
        Returns:
            Dict: 评估指标
        """
        total_queries = len(original_df)
        total_matches = len(matches_df)
        
        if sheet_name == 'Sheet8':
            # Sheet8是负样本，匹配越少越好
            return self._evaluate_negative_sample(matches_df, original_df)
        else:
            # 正常评估
            return self._evaluate_positive_sample(matches_df, original_df, sheet_name)
    
    def _evaluate_positive_sample(self, 
                                matches_df: pd.DataFrame,
                                original_df: pd.DataFrame,
                                sheet_name: str) -> Dict:
        """
        评估正样本
        
        Args:
            matches_df: 匹配结果
            original_df: 原始数据
            sheet_name: sheet名称
            
        Returns:
            Dict: 评估指标
        """
        total_queries = len(original_df)
        total_matches = len(matches_df)
        
        # 对于test_02，我们假设每个查询都应该有匹配（除了Sheet8）
        # 真正例是找到匹配的查询数量
        true_positives = total_matches
        false_positives = 0  # 在这个场景中，我们假设匹配都是正确的
        false_negatives = total_queries - total_matches
        true_negatives = 0
        
        # 计算指标
        precision = 1.0 if total_matches > 0 else 0.0  # 假设所有匹配都是正确的
        recall = true_positives / total_queries if total_queries > 0 else 0.0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
        accuracy = true_positives / total_queries if total_queries > 0 else 0.0
        
        return {
            'sheet_name': sheet_name,
            'total_queries': total_queries,
            'total_matches': total_matches,
            'true_positives': true_positives,
            'false_positives': false_positives,
            'false_negatives': false_negatives,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'accuracy': accuracy,
            'match_rate': total_matches / total_queries if total_queries > 0 else 0.0,
            'avg_similarity': matches_df['similarity_score'].mean() if len(matches_df) > 0 else 0.0,
            'similarity_distribution': self._get_similarity_distribution(matches_df),
            'data_transformation': self._get_transformation_description(sheet_name)
        }
    
    def _evaluate_negative_sample(self, 
                                matches_df: pd.DataFrame,
                                original_df: pd.DataFrame) -> Dict:
        """
        评估负样本（Sheet8）
        
        Args:
            matches_df: 匹配结果
            original_df: 原始数据
            
        Returns:
            Dict: 评估指标
        """
        total_queries = len(original_df)
        total_matches = len(matches_df)
        
        # 对于负样本，匹配就是假正例
        true_positives = 0
        false_positives = total_matches
        false_negatives = 0
        true_negatives = total_queries - total_matches
        
        # 计算指标
        precision = 0.0  # 对于负样本，理想情况下precision应该是0
        recall = 1.0  # 对于负样本，我们希望recall是1（正确拒绝所有）
        specificity = true_negatives / total_queries if total_queries > 0 else 0.0
        false_positive_rate = false_positives / total_queries if total_queries > 0 else 0.0
        
        return {
            'sheet_name': 'Sheet8',
            'total_queries': total_queries,
            'total_matches': total_matches,
            'true_positives': true_positives,
            'false_positives': false_positives,
            'false_negatives': false_negatives,
            'true_negatives': true_negatives,
            'precision': precision,
            'recall': recall,
            'specificity': specificity,
            'false_positive_rate': false_positive_rate,
            'accuracy': true_negatives / total_queries if total_queries > 0 else 0.0,
            'match_rate': total_matches / total_queries if total_queries > 0 else 0.0,
            'avg_similarity': matches_df['similarity_score'].mean() if len(matches_df) > 0 else 0.0,
            'similarity_distribution': self._get_similarity_distribution(matches_df),
            'data_transformation': 'Negative Sample (No true matches expected)',
            'performance_note': 'Lower match rate indicates better performance for negative samples'
        }
    
    def _get_similarity_distribution(self, matches_df: pd.DataFrame) -> Dict:
        """
        获取相似度分布
        
        Args:
            matches_df: 匹配结果
            
        Returns:
            Dict: 相似度分布
        """
        if len(matches_df) == 0:
            return {
                '0.9+': 0,
                '0.8-0.9': 0,
                '0.7-0.8': 0,
                '0.6-0.7': 0,
                '<0.6': 0
            }
        
        scores = matches_df['similarity_score']
        return {
            '0.9+': len(scores[scores >= 0.9]),
            '0.8-0.9': len(scores[(scores >= 0.8) & (scores < 0.9)]),
            '0.7-0.8': len(scores[(scores >= 0.7) & (scores < 0.8)]),
            '0.6-0.7': len(scores[(scores >= 0.6) & (scores < 0.7)]),
            '<0.6': len(scores[scores < 0.6])
        }
    
    def _get_transformation_description(self, sheet_name: str) -> str:
        """
        获取数据变换描述
        
        Args:
            sheet_name: sheet名称
            
        Returns:
            str: 变换描述
        """
        descriptions = {
            'Sheet1': 'Remove all special characters and white spaces',
            'Sheet2': 'Shuffle words then remove special characters',
            'Sheet3': 'Separate every character by white space',
            'Sheet4': 'Shuffle words then separate every character by white space',
            'Sheet5': 'Word removal',
            'Sheet6': 'Word truncation',
            'Sheet7': 'Unknown transformation',
            'Sheet8': 'Negative sample (no true matches expected)'
        }
        return descriptions.get(sheet_name, 'Unknown transformation')
    
    def generate_summary(self, evaluation_results: Dict) -> Dict:
        """
        生成评估总结
        
        Args:
            evaluation_results: 评估结果
            
        Returns:
            Dict: 总结信息
        """
        summary = {
            'overall_performance': {},
            'best_performing_sheets': [],
            'worst_performing_sheets': [],
            'recommendations': []
        }
        
        # 收集所有F1分数
        f1_scores = []
        sheet_performances = []
        
        # Test_01
        if 'test_01' in evaluation_results and evaluation_results['test_01']:
            test_01_f1 = evaluation_results['test_01'].get('f1_score', 0.0)
            f1_scores.append(test_01_f1)
            sheet_performances.append(('test_01', test_01_f1))
        
        # Test_02 sheets
        if 'test_02' in evaluation_results:
            for sheet_name, sheet_eval in evaluation_results['test_02'].items():
                if sheet_name != 'Sheet8':  # 排除负样本
                    f1 = sheet_eval.get('f1_score', 0.0)
                    f1_scores.append(f1)
                    sheet_performances.append((sheet_name, f1))
        
        # 计算总体性能
        if f1_scores:
            summary['overall_performance'] = {
                'avg_f1_score': np.mean(f1_scores),
                'min_f1_score': np.min(f1_scores),
                'max_f1_score': np.max(f1_scores),
                'std_f1_score': np.std(f1_scores)
            }
            
            # 排序性能
            sheet_performances.sort(key=lambda x: x[1], reverse=True)
            summary['best_performing_sheets'] = sheet_performances[:3]
            summary['worst_performing_sheets'] = sheet_performances[-3:]
        
        # 生成建议
        summary['recommendations'] = self._generate_recommendations(evaluation_results)
        
        return summary
    
    def _generate_recommendations(self, evaluation_results: Dict) -> List[str]:
        """
        生成改进建议
        
        Args:
            evaluation_results: 评估结果
            
        Returns:
            List[str]: 建议列表
        """
        recommendations = []
        
        # 检查Sheet8性能
        if 'test_02' in evaluation_results and 'Sheet8' in evaluation_results['test_02']:
            sheet8_eval = evaluation_results['test_02']['Sheet8']
            fpr = sheet8_eval.get('false_positive_rate', 0.0)
            
            if fpr > 0.05:  # 假正例率超过5%
                recommendations.append(
                    f"Sheet8假正例率为{fpr:.2%}，建议提高匹配阈值或改进负样本检测算法"
                )
            else:
                recommendations.append(
                    f"Sheet8假正例率为{fpr:.2%}，负样本检测性能良好"
                )
        
        # 检查整体性能
        if 'overall_performance' in evaluation_results.get('summary', {}):
            avg_f1 = evaluation_results['summary']['overall_performance'].get('avg_f1_score', 0.0)
            
            if avg_f1 < 0.6:
                recommendations.append("整体F1分数较低，建议优化相似度计算算法或调整匹配阈值")
            elif avg_f1 < 0.8:
                recommendations.append("整体性能中等，可以考虑使用集成方法或特征工程改进")
            else:
                recommendations.append("整体性能良好，算法表现优秀")
        
        return recommendations
