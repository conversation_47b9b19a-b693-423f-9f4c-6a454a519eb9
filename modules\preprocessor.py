#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本预处理模块
Text Preprocessing Module
"""

import re
import string
import pandas as pd
import numpy as np
from typing import List, Set, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class TextPreprocessor:
    """文本预处理器类"""
    
    def __init__(self):
        # 常见停用词
        self.stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'through', 'during',
            'before', 'after', 'above', 'below', 'between', 'among', 'a', 'an',
            'as', 'are', 'was', 'were', 'been', 'be', 'have', 'has', 'had',
            'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
            'might', 'must', 'can', 'shall', 'ltd', 'limited', 'inc', 'corp',
            'corporation', 'company', 'co', 'sa', 'llc', 'plc', 'gmbh'
        }
        
        # 常见公司后缀
        self.company_suffixes = {
            'ltd', 'limited', 'inc', 'incorporated', 'corp', 'corporation',
            'company', 'co', 'sa', 'llc', 'plc', 'gmbh', 'ag', 'nv', 'bv',
            'spa', 'srl', 'ltda', 'pty', 'pvt', 'private', 'public'
        }
    
    def clean_text(self, text: str) -> str:
        """
        基础文本清理
        
        Args:
            text: 输入文本
            
        Returns:
            str: 清理后的文本
        """
        if pd.isna(text) or not isinstance(text, str):
            return ""
        
        # 转换为小写
        text = text.lower().strip()
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text)
        
        return text
    
    def remove_special_characters(self, text: str, keep_spaces: bool = True) -> str:
        """
        移除特殊字符
        
        Args:
            text: 输入文本
            keep_spaces: 是否保留空格
            
        Returns:
            str: 处理后的文本
        """
        if pd.isna(text) or not isinstance(text, str):
            return ""
        
        if keep_spaces:
            # 保留字母、数字和空格
            text = re.sub(r'[^a-zA-Z0-9\s]', '', text)
        else:
            # 只保留字母和数字
            text = re.sub(r'[^a-zA-Z0-9]', '', text)
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def normalize_text(self, text: str) -> str:
        """
        标准化文本
        
        Args:
            text: 输入文本
            
        Returns:
            str: 标准化后的文本
        """
        if pd.isna(text) or not isinstance(text, str):
            return ""
        
        # 基础清理
        text = self.clean_text(text)
        
        # 移除标点符号（保留空格）
        text = text.translate(str.maketrans('', '', string.punctuation))
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def tokenize(self, text: str) -> List[str]:
        """
        分词
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 词汇列表
        """
        if pd.isna(text) or not isinstance(text, str):
            return []
        
        # 标准化文本
        text = self.normalize_text(text)
        
        # 分词
        tokens = text.split()
        
        # 过滤空词
        tokens = [token for token in tokens if token.strip()]
        
        return tokens
    
    def remove_stop_words(self, tokens: List[str]) -> List[str]:
        """
        移除停用词
        
        Args:
            tokens: 词汇列表
            
        Returns:
            List[str]: 过滤后的词汇列表
        """
        return [token for token in tokens if token.lower() not in self.stop_words]
    
    def extract_meaningful_tokens(self, text: str) -> List[str]:
        """
        提取有意义的词汇
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 有意义的词汇列表
        """
        # 分词
        tokens = self.tokenize(text)
        
        # 移除停用词
        tokens = self.remove_stop_words(tokens)
        
        # 过滤短词（长度小于2的词）
        tokens = [token for token in tokens if len(token) >= 2]
        
        return tokens
    
    def preprocess_for_sheet1(self, text: str) -> str:
        """
        Sheet1预处理：移除所有特殊字符和空格
        
        Args:
            text: 输入文本
            
        Returns:
            str: 处理后的文本
        """
        return self.remove_special_characters(text, keep_spaces=False)
    
    def preprocess_for_sheet2(self, text: str) -> str:
        """
        Sheet2预处理：打乱单词顺序然后移除特殊字符
        
        Args:
            text: 输入文本
            
        Returns:
            str: 处理后的文本
        """
        # 先分词
        tokens = self.tokenize(text)
        
        # 移除特殊字符
        clean_tokens = []
        for token in tokens:
            clean_token = self.remove_special_characters(token, keep_spaces=False)
            if clean_token:
                clean_tokens.append(clean_token)
        
        # 排序（模拟打乱后的效果，用于匹配）
        clean_tokens.sort()
        
        return ''.join(clean_tokens)
    
    def preprocess_for_sheet3(self, text: str) -> str:
        """
        Sheet3预处理：每个字符之间用空格分隔
        
        Args:
            text: 输入文本
            
        Returns:
            str: 处理后的文本
        """
        # 移除特殊字符
        clean_text = self.remove_special_characters(text, keep_spaces=False)
        
        # 在每个字符之间插入空格
        return ' '.join(clean_text)
    
    def preprocess_for_sheet4(self, text: str) -> str:
        """
        Sheet4预处理：打乱单词顺序然后字符分离
        
        Args:
            text: 输入文本
            
        Returns:
            str: 处理后的文本
        """
        # 先按Sheet2处理
        sheet2_result = self.preprocess_for_sheet2(text)
        
        # 再按Sheet3处理
        return ' '.join(sheet2_result)
    
    def preprocess_for_sheet5(self, text: str) -> str:
        """
        Sheet5预处理：单词移除（保留主要词汇）
        
        Args:
            text: 输入文本
            
        Returns:
            str: 处理后的文本
        """
        # 提取有意义的词汇
        tokens = self.extract_meaningful_tokens(text)
        
        # 只保留较长的词汇（长度>=3）
        important_tokens = [token for token in tokens if len(token) >= 3]
        
        return ' '.join(important_tokens)
    
    def preprocess_for_sheet6(self, text: str) -> str:
        """
        Sheet6预处理：单词截断
        
        Args:
            text: 输入文本
            
        Returns:
            str: 处理后的文本
        """
        # 分词
        tokens = self.tokenize(text)
        
        # 截断每个词（保留前3-5个字符）
        truncated_tokens = []
        for token in tokens:
            if len(token) > 5:
                truncated_tokens.append(token[:5])
            elif len(token) > 3:
                truncated_tokens.append(token[:3])
            else:
                truncated_tokens.append(token)
        
        return ' '.join(truncated_tokens)
    
    def preprocess_by_sheet_type(self, text: str, sheet_name: str) -> str:
        """
        根据sheet类型进行预处理
        
        Args:
            text: 输入文本
            sheet_name: sheet名称
            
        Returns:
            str: 处理后的文本
        """
        if sheet_name == 'Sheet1':
            return self.preprocess_for_sheet1(text)
        elif sheet_name == 'Sheet2':
            return self.preprocess_for_sheet2(text)
        elif sheet_name == 'Sheet3':
            return self.preprocess_for_sheet3(text)
        elif sheet_name == 'Sheet4':
            return self.preprocess_for_sheet4(text)
        elif sheet_name == 'Sheet5':
            return self.preprocess_for_sheet5(text)
        elif sheet_name == 'Sheet6':
            return self.preprocess_for_sheet6(text)
        else:
            # 默认标准化处理
            return self.normalize_text(text)
    
    def create_search_variants(self, text: str) -> List[str]:
        """
        创建搜索变体（用于提高匹配率）
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 搜索变体列表
        """
        variants = []
        
        # 原始文本
        variants.append(text)
        
        # 标准化版本
        normalized = self.normalize_text(text)
        if normalized and normalized != text:
            variants.append(normalized)
        
        # 移除特殊字符版本
        no_special = self.remove_special_characters(text, keep_spaces=True)
        if no_special and no_special not in variants:
            variants.append(no_special)
        
        # 移除所有非字母数字字符版本
        alphanumeric = self.remove_special_characters(text, keep_spaces=False)
        if alphanumeric and alphanumeric not in variants:
            variants.append(alphanumeric)
        
        # 只保留有意义词汇版本
        meaningful_tokens = self.extract_meaningful_tokens(text)
        if meaningful_tokens:
            meaningful_text = ' '.join(meaningful_tokens)
            if meaningful_text and meaningful_text not in variants:
                variants.append(meaningful_text)
        
        return variants
