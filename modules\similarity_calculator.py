#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相似度计算模块
Similarity Calculation Module
"""

import re
import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Set
from difflib import SequenceMatcher
import logging

logger = logging.getLogger(__name__)

class SimilarityCalculator:
    """相似度计算器类"""
    
    def __init__(self):
        pass
    
    def levenshtein_distance(self, s1: str, s2: str) -> int:
        """
        计算Levenshtein距离
        
        Args:
            s1, s2: 两个字符串
            
        Returns:
            int: Levenshtein距离
        """
        if len(s1) < len(s2):
            return self.levenshtein_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def levenshtein_similarity(self, s1: str, s2: str) -> float:
        """
        计算基于Levenshtein距离的相似度
        
        Args:
            s1, s2: 两个字符串
            
        Returns:
            float: 相似度 (0-1)
        """
        if not s1 and not s2:
            return 1.0
        if not s1 or not s2:
            return 0.0
        
        max_len = max(len(s1), len(s2))
        distance = self.levenshtein_distance(s1, s2)
        return 1.0 - (distance / max_len)
    
    def jaro_similarity(self, s1: str, s2: str) -> float:
        """
        计算Jaro相似度
        
        Args:
            s1, s2: 两个字符串
            
        Returns:
            float: Jaro相似度 (0-1)
        """
        if not s1 and not s2:
            return 1.0
        if not s1 or not s2:
            return 0.0
        
        len1, len2 = len(s1), len(s2)
        
        # 匹配窗口
        match_window = max(len1, len2) // 2 - 1
        if match_window < 0:
            match_window = 0
        
        s1_matches = [False] * len1
        s2_matches = [False] * len2
        
        matches = 0
        transpositions = 0
        
        # 找到匹配字符
        for i in range(len1):
            start = max(0, i - match_window)
            end = min(i + match_window + 1, len2)
            
            for j in range(start, end):
                if s2_matches[j] or s1[i] != s2[j]:
                    continue
                s1_matches[i] = s2_matches[j] = True
                matches += 1
                break
        
        if matches == 0:
            return 0.0
        
        # 计算转置
        k = 0
        for i in range(len1):
            if not s1_matches[i]:
                continue
            while not s2_matches[k]:
                k += 1
            if s1[i] != s2[k]:
                transpositions += 1
            k += 1
        
        jaro = (matches / len1 + matches / len2 + (matches - transpositions / 2) / matches) / 3.0
        return jaro
    
    def jaro_winkler_similarity(self, s1: str, s2: str, prefix_scale: float = 0.1) -> float:
        """
        计算Jaro-Winkler相似度
        
        Args:
            s1, s2: 两个字符串
            prefix_scale: 前缀权重
            
        Returns:
            float: Jaro-Winkler相似度 (0-1)
        """
        jaro_sim = self.jaro_similarity(s1, s2)
        
        if jaro_sim < 0.7:
            return jaro_sim
        
        # 计算公共前缀长度（最多4个字符）
        prefix_len = 0
        for i in range(min(len(s1), len(s2), 4)):
            if s1[i] == s2[i]:
                prefix_len += 1
            else:
                break
        
        return jaro_sim + (prefix_len * prefix_scale * (1 - jaro_sim))
    
    def jaccard_similarity(self, s1: str, s2: str) -> float:
        """
        计算Jaccard相似度（基于字符集合）
        
        Args:
            s1, s2: 两个字符串
            
        Returns:
            float: Jaccard相似度 (0-1)
        """
        if not s1 and not s2:
            return 1.0
        if not s1 or not s2:
            return 0.0
        
        set1 = set(s1.lower())
        set2 = set(s2.lower())
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def jaccard_token_similarity(self, s1: str, s2: str) -> float:
        """
        计算基于词汇的Jaccard相似度
        
        Args:
            s1, s2: 两个字符串
            
        Returns:
            float: Jaccard相似度 (0-1)
        """
        if not s1 and not s2:
            return 1.0
        if not s1 or not s2:
            return 0.0
        
        tokens1 = set(s1.lower().split())
        tokens2 = set(s2.lower().split())
        
        intersection = len(tokens1.intersection(tokens2))
        union = len(tokens1.union(tokens2))
        
        return intersection / union if union > 0 else 0.0
    
    def dice_similarity(self, s1: str, s2: str) -> float:
        """
        计算Dice相似度
        
        Args:
            s1, s2: 两个字符串
            
        Returns:
            float: Dice相似度 (0-1)
        """
        if not s1 and not s2:
            return 1.0
        if not s1 or not s2:
            return 0.0
        
        set1 = set(s1.lower())
        set2 = set(s2.lower())
        
        intersection = len(set1.intersection(set2))
        
        return (2.0 * intersection) / (len(set1) + len(set2))
    
    def longest_common_subsequence(self, s1: str, s2: str) -> int:
        """
        计算最长公共子序列长度
        
        Args:
            s1, s2: 两个字符串
            
        Returns:
            int: LCS长度
        """
        m, n = len(s1), len(s2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i-1] == s2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])
        
        return dp[m][n]
    
    def lcs_similarity(self, s1: str, s2: str) -> float:
        """
        计算基于LCS的相似度
        
        Args:
            s1, s2: 两个字符串
            
        Returns:
            float: LCS相似度 (0-1)
        """
        if not s1 and not s2:
            return 1.0
        if not s1 or not s2:
            return 0.0
        
        lcs_len = self.longest_common_subsequence(s1, s2)
        max_len = max(len(s1), len(s2))
        
        return lcs_len / max_len if max_len > 0 else 0.0
    
    def sequence_matcher_similarity(self, s1: str, s2: str) -> float:
        """
        使用Python内置SequenceMatcher计算相似度
        
        Args:
            s1, s2: 两个字符串
            
        Returns:
            float: 相似度 (0-1)
        """
        if not s1 and not s2:
            return 1.0
        if not s1 or not s2:
            return 0.0
        
        return SequenceMatcher(None, s1.lower(), s2.lower()).ratio()
    
    def character_ngram_similarity(self, s1: str, s2: str, n: int = 2) -> float:
        """
        计算字符n-gram相似度
        
        Args:
            s1, s2: 两个字符串
            n: n-gram大小
            
        Returns:
            float: n-gram相似度 (0-1)
        """
        if not s1 and not s2:
            return 1.0
        if not s1 or not s2:
            return 0.0
        
        def get_ngrams(text: str, n: int) -> Set[str]:
            text = text.lower()
            return set(text[i:i+n] for i in range(len(text) - n + 1))
        
        ngrams1 = get_ngrams(s1, n)
        ngrams2 = get_ngrams(s2, n)
        
        if not ngrams1 and not ngrams2:
            return 1.0
        if not ngrams1 or not ngrams2:
            return 0.0
        
        intersection = len(ngrams1.intersection(ngrams2))
        union = len(ngrams1.union(ngrams2))
        
        return intersection / union if union > 0 else 0.0
    
    def combined_similarity(self, s1: str, s2: str, weights: Dict[str, float] = None) -> float:
        """
        计算组合相似度
        
        Args:
            s1, s2: 两个字符串
            weights: 各种相似度的权重
            
        Returns:
            float: 组合相似度 (0-1)
        """
        if weights is None:
            weights = {
                'levenshtein': 0.2,
                'jaro_winkler': 0.25,
                'jaccard_token': 0.2,
                'lcs': 0.15,
                'sequence_matcher': 0.2
            }
        
        similarities = {
            'levenshtein': self.levenshtein_similarity(s1, s2),
            'jaro_winkler': self.jaro_winkler_similarity(s1, s2),
            'jaccard_token': self.jaccard_token_similarity(s1, s2),
            'lcs': self.lcs_similarity(s1, s2),
            'sequence_matcher': self.sequence_matcher_similarity(s1, s2)
        }
        
        combined_score = sum(similarities[method] * weight 
                           for method, weight in weights.items() 
                           if method in similarities)
        
        return combined_score
    
    def adaptive_similarity(self, s1: str, s2: str, sheet_type: str = None) -> float:
        """
        自适应相似度计算（根据数据类型调整）
        
        Args:
            s1, s2: 两个字符串
            sheet_type: 数据表类型
            
        Returns:
            float: 相似度 (0-1)
        """
        if sheet_type == 'Sheet1':
            # 移除特殊字符的数据，重点关注字符匹配
            return self.combined_similarity(s1, s2, {
                'levenshtein': 0.3,
                'jaro_winkler': 0.3,
                'jaccard': 0.2,
                'sequence_matcher': 0.2
            })
        elif sheet_type == 'Sheet2':
            # 单词打乱的数据，重点关注词汇匹配
            return self.combined_similarity(s1, s2, {
                'jaccard_token': 0.4,
                'dice': 0.3,
                'sequence_matcher': 0.3
            })
        elif sheet_type == 'Sheet3' or sheet_type == 'Sheet4':
            # 字符分离的数据，重点关注字符序列
            return self.combined_similarity(s1, s2, {
                'lcs': 0.4,
                'sequence_matcher': 0.3,
                'levenshtein': 0.3
            })
        elif sheet_type == 'Sheet5':
            # 单词移除的数据，重点关注剩余词汇
            return self.combined_similarity(s1, s2, {
                'jaccard_token': 0.5,
                'jaro_winkler': 0.3,
                'lcs': 0.2
            })
        elif sheet_type == 'Sheet6':
            # 单词截断的数据，重点关注前缀匹配
            return self.combined_similarity(s1, s2, {
                'jaro_winkler': 0.4,
                'levenshtein': 0.3,
                'sequence_matcher': 0.3
            })
        else:
            # 默认组合相似度
            return self.combined_similarity(s1, s2)
