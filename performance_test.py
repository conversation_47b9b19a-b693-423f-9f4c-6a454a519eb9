#!/usr/bin/env python3
"""
性能测试脚本 - 对比原始算法和优化算法的性能
"""

import pandas as pd
import time
import logging
from modules.matcher import EntityMatcher
from modules.data_loader import DataLoader

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def performance_test():
    """性能测试主函数"""
    print("🚀 实体匹配性能测试")
    print("=" * 50)
    
    # 加载数据
    print("📊 加载测试数据...")
    loader = DataLoader()
    
    # 加载Primary数据
    primary_df = pd.read_csv('DATA/primary.csv')
    print(f"Primary数据: {len(primary_df)} 条记录")

    # 加载Test_01数据
    test_01_df = pd.read_csv('DATA/test_01.csv')
    print(f"Test_01数据: {len(test_01_df)} 条记录")
    
    # 创建小样本测试（避免测试时间过长）
    test_sample = test_01_df.head(1000)  # 取前1000条记录测试
    print(f"测试样本: {len(test_sample)} 条记录")
    
    # 初始化匹配器
    matcher = EntityMatcher()
    
    print("\n🔄 开始性能测试...")
    print("-" * 50)
    
    # 测试1: 原始算法
    print("🐌 测试原始算法...")
    start_time = time.time()
    
    original_matches = matcher.match_entities(
        test_sample,
        primary_df,
        threshold=0.7,
        method='combined',
        query_col='VARIANT',
        target_col='NAME'
    )
    
    original_time = time.time() - start_time
    print(f"原始算法结果: {len(original_matches)} 个匹配")
    print(f"原始算法耗时: {original_time:.2f} 秒")
    print(f"原始算法速度: {len(test_sample)/original_time:.1f} 条/秒")
    
    # 测试2: 优化算法
    print("\n🚀 测试优化算法...")
    start_time = time.time()
    
    optimized_matches = matcher.fast_match_entities(
        test_sample,
        primary_df,
        threshold=0.7,
        method='combined',
        query_col='VARIANT',
        target_col='NAME'
    )
    
    optimized_time = time.time() - start_time
    print(f"优化算法结果: {len(optimized_matches)} 个匹配")
    print(f"优化算法耗时: {optimized_time:.2f} 秒")
    print(f"优化算法速度: {len(test_sample)/optimized_time:.1f} 条/秒")
    
    # 性能对比
    print("\n📈 性能对比结果")
    print("=" * 50)
    
    speedup = original_time / optimized_time if optimized_time > 0 else float('inf')
    time_saved = original_time - optimized_time
    time_saved_percent = (time_saved / original_time) * 100 if original_time > 0 else 0
    
    print(f"⚡ 性能提升: {speedup:.2f}x")
    print(f"⏱️  时间节省: {time_saved:.2f} 秒 ({time_saved_percent:.1f}%)")
    print(f"🎯 匹配质量: {len(original_matches)} vs {len(optimized_matches)} 个匹配")
    
    # 预估全量数据处理时间
    full_data_size = len(test_01_df)
    estimated_original_time = (original_time / len(test_sample)) * full_data_size
    estimated_optimized_time = (optimized_time / len(test_sample)) * full_data_size
    
    print(f"\n🔮 全量数据预估时间 ({full_data_size} 条记录):")
    print(f"原始算法预估: {estimated_original_time/60:.1f} 分钟")
    print(f"优化算法预估: {estimated_optimized_time/60:.1f} 分钟")
    print(f"预估时间节省: {(estimated_original_time - estimated_optimized_time)/60:.1f} 分钟")
    
    # 质量对比
    print(f"\n🔍 匹配质量对比:")
    if len(original_matches) > 0 and len(optimized_matches) > 0:
        # 计算平均相似度
        orig_avg_score = original_matches['similarity_score'].mean()
        opt_avg_score = optimized_matches['similarity_score'].mean()
        
        print(f"原始算法平均相似度: {orig_avg_score:.3f}")
        print(f"优化算法平均相似度: {opt_avg_score:.3f}")
        print(f"相似度差异: {abs(orig_avg_score - opt_avg_score):.3f}")
        
        # 检查匹配一致性
        orig_ids = set(original_matches['matched_id'].tolist())
        opt_ids = set(optimized_matches['matched_id'].tolist())
        
        common_matches = len(orig_ids & opt_ids)
        total_unique = len(orig_ids | opt_ids)
        consistency = common_matches / total_unique if total_unique > 0 else 0
        
        print(f"匹配一致性: {consistency:.3f} ({common_matches}/{total_unique})")
    
    print("\n✅ 性能测试完成!")
    
    return {
        'original_time': original_time,
        'optimized_time': optimized_time,
        'speedup': speedup,
        'original_matches': len(original_matches),
        'optimized_matches': len(optimized_matches),
        'test_size': len(test_sample)
    }

if __name__ == '__main__':
    try:
        results = performance_test()
        print(f"\n🎉 测试成功完成!")
        print(f"性能提升: {results['speedup']:.2f}x")
        
    except Exception as e:
        logger.error(f"性能测试失败: {str(e)}")
        print(f"❌ 测试失败: {str(e)}")
