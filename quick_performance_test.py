#!/usr/bin/env python3
"""
快速性能测试 - 使用小样本快速对比性能
"""

import pandas as pd
import time
import logging
from modules.matcher import EntityMatcher

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def quick_performance_test():
    """快速性能测试"""
    print("⚡ 快速性能测试 (小样本)")
    print("=" * 40)
    
    # 加载数据
    print("📊 加载测试数据...")
    primary_df = pd.read_csv('DATA/primary.csv')
    test_01_df = pd.read_csv('DATA/test_01.csv')
    
    # 创建小样本测试
    test_sample = test_01_df.head(50)  # 只测试50条记录
    target_sample = primary_df.head(500)  # 目标数据也减少
    
    print(f"测试样本: {len(test_sample)} 条记录")
    print(f"目标样本: {len(target_sample)} 条记录")
    
    # 初始化匹配器
    matcher = EntityMatcher()
    
    print("\n🔄 开始快速测试...")
    print("-" * 40)
    
    # 测试1: 原始算法
    print("🐌 测试原始算法...")
    start_time = time.time()
    
    original_matches = matcher.match_entities(
        test_sample,
        target_sample,
        threshold=0.7,
        method='combined',
        query_col='VARIANT',
        target_col='NAME'
    )
    
    original_time = time.time() - start_time
    print(f"✅ 原始算法: {len(original_matches)} 个匹配, {original_time:.2f}秒")
    
    # 测试2: 优化算法
    print("🚀 测试优化算法...")
    start_time = time.time()
    
    optimized_matches = matcher.fast_match_entities(
        test_sample,
        target_sample,
        threshold=0.7,
        method='combined',
        query_col='VARIANT',
        target_col='NAME'
    )
    
    optimized_time = time.time() - start_time
    print(f"✅ 优化算法: {len(optimized_matches)} 个匹配, {optimized_time:.2f}秒")
    
    # 性能对比
    print("\n📈 性能对比")
    print("=" * 40)
    
    if optimized_time > 0:
        speedup = original_time / optimized_time
        time_saved_percent = ((original_time - optimized_time) / original_time) * 100
        
        print(f"⚡ 性能提升: {speedup:.2f}x")
        print(f"⏱️  时间节省: {time_saved_percent:.1f}%")
        print(f"🎯 匹配数量: {len(original_matches)} vs {len(optimized_matches)}")
        
        # 预估全量数据时间
        full_records = 16041
        full_target = 16041
        
        # 基于样本比例预估
        scale_factor = (full_records * full_target) / (len(test_sample) * len(target_sample))
        
        estimated_original = original_time * scale_factor
        estimated_optimized = optimized_time * scale_factor
        
        print(f"\n🔮 全量数据预估:")
        print(f"原始算法: {estimated_original/3600:.1f} 小时")
        print(f"优化算法: {estimated_optimized/60:.1f} 分钟")
        print(f"时间节省: {(estimated_original - estimated_optimized)/3600:.1f} 小时")
        
        # 处理速度
        orig_speed = len(test_sample) / original_time
        opt_speed = len(test_sample) / optimized_time
        
        print(f"\n🏃 处理速度:")
        print(f"原始算法: {orig_speed:.1f} 条/秒")
        print(f"优化算法: {opt_speed:.1f} 条/秒")
        
    else:
        print("⚠️  优化算法时间太短，无法准确计算")
    
    print("\n✅ 快速测试完成!")
    
    return {
        'original_time': original_time,
        'optimized_time': optimized_time,
        'original_matches': len(original_matches),
        'optimized_matches': len(optimized_matches)
    }

if __name__ == '__main__':
    try:
        results = quick_performance_test()
        
        if results['optimized_time'] > 0:
            speedup = results['original_time'] / results['optimized_time']
            print(f"\n🎉 优化成功! 性能提升 {speedup:.2f}x")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        print(f"❌ 测试失败: {str(e)}")
