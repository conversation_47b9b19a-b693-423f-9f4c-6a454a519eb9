#!/usr/bin/env python3
"""
快速测试Sheet7的改进效果
"""

import pandas as pd
from modules.matcher import EntityMatcher
from modules.data_loader import DataLoader
import time

def quick_sheet7_test():
    print("=== 快速测试Sheet7改进效果 ===")
    
    # 加载数据
    print("加载数据...")
    loader = DataLoader()
    
    # 加载primary数据
    primary_df = pd.read_csv('DATA/primary.csv')
    print(f"Primary数据: {len(primary_df)} 条")
    
    # 加载Sheet7数据
    sheet7_df = pd.read_excel('DATA/test_02.xlsx', sheet_name='Sheet7')
    print(f"Sheet7数据: {len(sheet7_df)} 条")
    
    # 创建匹配器
    matcher = EntityMatcher()
    
    # 测试前10个Sheet7记录
    print("\n=== 测试前10个Sheet7记录 ===")
    test_df = sheet7_df.head(10)
    
    start_time = time.time()
    
    matches = []
    for idx, row in test_df.iterrows():
        query_id = row['ID']
        query_name = row['NAME']
        
        # 在primary中查找对应的原始名称
        primary_match = primary_df[primary_df['ID'] == query_id]
        if len(primary_match) > 0:
            original_name = primary_match['NAME'].values[0]
            
            # 测试匹配
            score = matcher._simple_similarity(original_name.upper().strip(), query_name.upper().strip())
            
            matches.append({
                'ID': query_id,
                'Original': original_name,
                'Sheet7': query_name,
                'Score': score,
                'Match': score >= 0.7
            })
    
    end_time = time.time()
    
    # 显示结果
    print(f"\n测试完成，耗时: {end_time - start_time:.2f}秒")
    print("\n详细结果:")
    print("-" * 100)
    
    match_count = 0
    for match in matches:
        is_match = match['Match']
        if is_match:
            match_count += 1
        
        print(f"ID: {match['ID']}")
        print(f"原名: {match['Original']}")
        print(f"缩写: {match['Sheet7']}")
        print(f"分数: {match['Score']:.3f} {'✓' if is_match else '✗'}")
        print("-" * 100)
    
    print(f"\n总结:")
    print(f"匹配成功: {match_count}/{len(matches)}")
    print(f"匹配率: {match_count/len(matches)*100:.1f}%")
    
    if match_count >= 7:  # 70%以上
        print("🎉 算法改进效果显著！")
    elif match_count >= 5:  # 50%以上
        print("✅ 算法改进有效果！")
    else:
        print("⚠️ 需要进一步优化")

if __name__ == "__main__":
    quick_sheet7_test()
