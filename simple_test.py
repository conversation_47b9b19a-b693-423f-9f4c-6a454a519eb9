#!/usr/bin/env python3
"""
简单测试
"""

import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.append(os.getcwd())

try:
    from modules.matcher import EntityMatcher
    
    print("=== 简单测试 ===")
    
    # 创建匹配器
    matcher = EntityMatcher()
    
    # 测试几个具体的例子
    test_cases = [
        ("SHELESTENKO, HENNADIY OLEKSANDROVYCH", "SHELESTENKO, HENNADIY O."),
        ("MALEKOUTI POUR, HAMIDREZA", "MALEKOUTI POUR, H."),
        ("AGUILAR GARCIA, MARVIN RAMIRO", "A GARCIA, MARVIN R"),
    ]
    
    print("测试结果:")
    for original, abbreviated in test_cases:
        score = matcher._simple_similarity(original, abbreviated)
        print(f"原名: {original}")
        print(f"缩写: {abbreviated}")
        print(f"分数: {score:.3f}")
        print(f"匹配: {'✓' if score >= 0.7 else '✗'}")
        print("-" * 50)
        
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
