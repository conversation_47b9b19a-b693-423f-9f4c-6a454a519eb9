/**
 * 进度条管理器
 * Progress Bar Manager
 */

class ProgressManager {
    constructor() {
        this.isRunning = false;
        this.intervalId = null;
        this.progressModal = null;
        this.progressBar = null;
        this.progressText = null;
        this.progressDetails = null;
        this.progressTime = null;
        this.startTime = null;
        
        this.initModal();
    }
    
    initModal() {
        // 创建进度条模态框
        const modalHtml = `
            <div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-cogs me-2"></i>
                                <span id="progressTitle">处理中...</span>
                            </h5>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span id="progressText">正在初始化...</span>
                                    <span id="progressPercentage">0%</span>
                                </div>
                                <div class="progress" style="height: 25px;">
                                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" style="width: 0%">
                                        <span id="progressBarText">0%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        <span id="progressDetails">准备开始处理...</span>
                                    </small>
                                </div>
                                <div class="col-md-6 text-end">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        已用时间: <span id="progressTime">0秒</span>
                                    </small>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <div class="alert alert-info mb-2">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    <strong>提示：</strong>处理过程可能需要几分钟时间，请耐心等待。您可以最小化浏览器窗口，处理完成后会自动跳转到结果页面。
                                </div>

                                <!-- 系统状态指示器 -->
                                <div class="alert alert-success mb-0" id="systemStatus">
                                    <div class="d-flex align-items-center">
                                        <div class="spinner-border spinner-border-sm me-2" role="status" id="systemSpinner">
                                            <span class="visually-hidden">运行中...</span>
                                        </div>
                                        <div>
                                            <strong>系统状态：</strong>
                                            <span id="systemStatusText">正在连接...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" id="minimizeBtn">
                                <i class="fas fa-window-minimize me-2"></i>
                                最小化
                            </button>
                            <button type="button" class="btn btn-danger" id="cancelBtn" style="display: none;">
                                <i class="fas fa-times me-2"></i>
                                取消
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // 获取元素引用
        this.progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
        this.progressBar = document.getElementById('progressBar');
        this.progressText = document.getElementById('progressText');
        this.progressDetails = document.getElementById('progressDetails');
        this.progressTime = document.getElementById('progressTime');
        
        // 绑定事件
        document.getElementById('minimizeBtn').addEventListener('click', () => {
            this.progressModal.hide();
        });
    }
    
    start(title = '处理中...') {
        this.isRunning = true;
        this.startTime = Date.now();
        
        // 设置标题
        document.getElementById('progressTitle').textContent = title;
        
        // 重置进度
        this.updateProgress(0, '正在初始化...', '准备开始处理...');
        
        // 显示模态框
        this.progressModal.show();
        
        // 开始轮询进度
        this.startPolling();
    }
    
    startPolling() {
        this.intervalId = setInterval(() => {
            this.fetchProgress();
        }, 1000); // 每秒更新一次
    }
    
    async fetchProgress() {
        try {
            const response = await fetch('/api/progress');

            // 检查网络连接状态
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            this.updateProgress(data.progress, data.message, data.details);
            this.updateTime(data.elapsed_time);

            // 更新系统状态指示器
            this.updateSystemStatus('running', '系统正常运行');

            // 检查是否完成
            if (data.status === 'completed') {
                this.complete(data.message);
            } else if (data.status === 'error') {
                this.error(data.message);
            }

        } catch (error) {
            console.error('获取进度失败:', error);
            this.updateSystemStatus('error', `连接失败: ${error.message}`);

            // 如果连续失败多次，显示错误
            this.errorCount = (this.errorCount || 0) + 1;
            if (this.errorCount >= 3) {
                this.error('系统连接中断，请检查网络或刷新页面');
            }
        }
    }
    
    updateProgress(progress, message = '', details = '') {
        // 更新进度条
        this.progressBar.style.width = `${progress}%`;
        this.progressBar.setAttribute('aria-valuenow', progress);
        
        // 更新文本
        document.getElementById('progressBarText').textContent = `${progress}%`;
        document.getElementById('progressPercentage').textContent = `${progress}%`;
        
        if (message) {
            this.progressText.textContent = message;
        }
        
        if (details) {
            this.progressDetails.textContent = details;
        }
        
        // 更新进度条颜色
        this.progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
        if (progress >= 100) {
            this.progressBar.classList.add('bg-success');
        } else if (progress >= 75) {
            this.progressBar.classList.add('bg-info');
        } else if (progress >= 50) {
            this.progressBar.classList.add('bg-warning');
        } else {
            this.progressBar.classList.add('bg-primary');
        }
    }
    
    updateTime(elapsedSeconds) {
        if (elapsedSeconds !== undefined) {
            this.progressTime.textContent = `${elapsedSeconds}秒`;
        } else if (this.startTime) {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            this.progressTime.textContent = `${elapsed}秒`;
        }
    }

    updateSystemStatus(status, message) {
        const statusElement = document.getElementById('systemStatus');
        const statusText = document.getElementById('systemStatusText');
        const spinner = document.getElementById('systemSpinner');

        if (!statusElement || !statusText || !spinner) return;

        // 重置错误计数
        if (status === 'running') {
            this.errorCount = 0;
        }

        statusText.textContent = message;

        // 更新状态样式
        statusElement.className = 'alert mb-0';
        spinner.style.display = 'inline-block';

        switch (status) {
            case 'running':
                statusElement.classList.add('alert-success');
                spinner.className = 'spinner-border spinner-border-sm me-2 text-success';
                break;
            case 'error':
                statusElement.classList.add('alert-danger');
                spinner.className = 'fas fa-exclamation-triangle me-2 text-danger';
                spinner.style.display = 'inline';
                break;
            case 'completed':
                statusElement.classList.add('alert-success');
                spinner.className = 'fas fa-check-circle me-2 text-success';
                spinner.style.display = 'inline';
                break;
            default:
                statusElement.classList.add('alert-info');
                spinner.className = 'spinner-border spinner-border-sm me-2 text-info';
        }
    }
    
    complete(message = '处理完成！') {
        this.isRunning = false;
        this.stopPolling();

        // 更新为成功状态
        this.updateProgress(100, message, '所有任务已完成');
        this.progressBar.classList.remove('progress-bar-animated');
        this.progressBar.classList.add('bg-success');

        // 更新系统状态
        this.updateSystemStatus('completed', '处理成功完成');

        // 3秒后自动关闭并刷新页面
        setTimeout(() => {
            this.progressModal.hide();
            window.location.reload();
        }, 3000);
    }
    
    error(message = '处理失败！') {
        this.isRunning = false;
        this.stopPolling();
        
        // 更新为错误状态
        this.progressBar.classList.remove('progress-bar-animated');
        this.progressBar.classList.add('bg-danger');
        this.progressText.textContent = message;
        this.progressDetails.textContent = '请检查错误信息并重试';
        
        // 显示关闭按钮
        document.getElementById('cancelBtn').style.display = 'inline-block';
        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.progressModal.hide();
        });
    }
    
    stopPolling() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    hide() {
        this.progressModal.hide();
    }
    
    destroy() {
        this.stopPolling();
        if (this.progressModal) {
            this.progressModal.dispose();
        }
    }
}

// 全局进度管理器实例
window.progressManager = new ProgressManager();

// 表单提交助手函数
function submitFormWithProgress(formId, title, successUrl = null) {
    const form = document.getElementById(formId);
    if (!form) {
        console.error(`表单 ${formId} 不存在`);
        return;
    }
    
    // 开始进度条
    window.progressManager.start(title);
    
    // 提交表单
    const formData = new FormData(form);
    
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'error') {
            window.progressManager.error(data.message);
        }
        // 如果成功，进度条会通过轮询自动更新
    })
    .catch(error => {
        console.error('提交失败:', error);
        window.progressManager.error('提交请求失败，请检查网络连接');
    });
}

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.progressManager) {
        window.progressManager.destroy();
    }
});
