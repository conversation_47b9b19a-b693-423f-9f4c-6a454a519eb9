{% extends "base.html" %}

{% block title %}首页 - 实体匹配与去重系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 欢迎区域 -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-home me-2"></i>
                    欢迎使用实体匹配与去重系统
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="text-primary">系统功能概述</h5>
                        <p class="lead">
                            本系统是一个专业的实体匹配与去重解决方案，专门用于处理HW1作业中的数据匹配任务。
                            系统集成了多种先进的文本相似度算法和智能匹配策略。
                        </p>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h6 class="text-secondary">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    核心功能
                                </h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-database text-primary me-2"></i>数据加载与验证</li>
                                    <li><i class="fas fa-filter text-primary me-2"></i>智能去重处理</li>
                                    <li><i class="fas fa-search text-primary me-2"></i>多策略实体匹配</li>
                                    <li><i class="fas fa-chart-line text-primary me-2"></i>性能评估分析</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-secondary">
                                    <i class="fas fa-cogs text-warning me-2"></i>
                                    技术特性
                                </h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-brain text-primary me-2"></i>多种相似度算法</li>
                                    <li><i class="fas fa-adjust text-primary me-2"></i>自适应匹配策略</li>
                                    <li><i class="fas fa-shield-alt text-primary me-2"></i>负样本检测</li>
                                    <li><i class="fas fa-download text-primary me-2"></i>结果批量导出</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-project-diagram" style="font-size: 120px; color: #667eea; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- 操作流程 -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-route me-2"></i>
                    操作流程指南
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                                <div class="stats-number">1</div>
                                <div class="stats-label">数据加载</div>
                            </div>
                            <p class="small text-muted">
                                加载primary.csv、alternate.csv、test_01.csv和test_02.xlsx文件
                            </p>
                            <a href="{{ url_for('load_data') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-database me-1"></i>开始加载
                            </a>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="stats-card" style="background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);">
                                <div class="stats-number">2</div>
                                <div class="stats-label">去重处理</div>
                            </div>
                            <p class="small text-muted">
                                对primary和alternate数据进行智能去重处理
                            </p>
                            <a href="{{ url_for('deduplication') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-filter me-1"></i>执行去重
                            </a>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                                <div class="stats-number">3</div>
                                <div class="stats-label">实体匹配</div>
                            </div>
                            <p class="small text-muted">
                                执行test_01和test_02各sheet的实体匹配
                            </p>
                            <a href="{{ url_for('matching') }}" class="btn btn-warning btn-sm">
                                <i class="fas fa-search me-1"></i>开始匹配
                            </a>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <div class="stats-number">4</div>
                                <div class="stats-label">性能评估</div>
                            </div>
                            <p class="small text-muted">
                                计算precision、recall、F1-score等评估指标
                            </p>
                            <a href="{{ url_for('evaluation') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-chart-line me-1"></i>查看评估
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- 数据集说明 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    数据集说明
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>数据文件</th>
                                <th>记录数</th>
                                <th>用途</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>primary.csv</strong></td>
                                <td>16,043</td>
                                <td>标准实体数据</td>
                            </tr>
                            <tr>
                                <td><strong>alternate.csv</strong></td>
                                <td>19,527</td>
                                <td>实体变体数据</td>
                            </tr>
                            <tr>
                                <td><strong>test_01.csv</strong></td>
                                <td>16,043</td>
                                <td>测试匹配数据</td>
                            </tr>
                            <tr>
                                <td><strong>test_02.xlsx</strong></td>
                                <td>8×1,000</td>
                                <td>变换测试数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <h6 class="text-secondary">Test_02 Sheet说明：</h6>
                    <ul class="list-unstyled small">
                        <li><strong>Sheet1:</strong> 移除特殊字符和空格</li>
                        <li><strong>Sheet2:</strong> 单词打乱+移除特殊字符</li>
                        <li><strong>Sheet3:</strong> 字符间插入空格</li>
                        <li><strong>Sheet4:</strong> 单词打乱+字符分离</li>
                        <li><strong>Sheet5:</strong> 单词移除</li>
                        <li><strong>Sheet6:</strong> 单词截断</li>
                        <li><strong>Sheet7:</strong> 待分析变换</li>
                        <li><strong>Sheet8:</strong> 负样本（无真实匹配）</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 算法说明 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-brain me-2"></i>
                    算法特性
                </h5>
            </div>
            <div class="card-body">
                <h6 class="text-secondary">相似度算法：</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-dot-circle text-primary me-2"></i><strong>Levenshtein距离</strong> - 字符编辑距离</li>
                    <li><i class="fas fa-dot-circle text-primary me-2"></i><strong>Jaro-Winkler</strong> - 前缀权重相似度</li>
                    <li><i class="fas fa-dot-circle text-primary me-2"></i><strong>Jaccard系数</strong> - 集合相似度</li>
                    <li><i class="fas fa-dot-circle text-primary me-2"></i><strong>LCS</strong> - 最长公共子序列</li>
                    <li><i class="fas fa-dot-circle text-primary me-2"></i><strong>组合算法</strong> - 多算法融合</li>
                </ul>
                
                <h6 class="text-secondary mt-3">匹配策略：</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-cog text-success me-2"></i>多层次匹配（精确→模糊→语义）</li>
                    <li><i class="fas fa-cog text-success me-2"></i>自适应阈值调整</li>
                    <li><i class="fas fa-cog text-success me-2"></i>负样本检测机制</li>
                    <li><i class="fas fa-cog text-success me-2"></i>文本预处理优化</li>
                </ul>
                
                <h6 class="text-secondary mt-3">评估指标：</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-chart-bar text-warning me-2"></i>Precision（精确率）</li>
                    <li><i class="fas fa-chart-bar text-warning me-2"></i>Recall（召回率）</li>
                    <li><i class="fas fa-chart-bar text-warning me-2"></i>F1-Score（综合指标）</li>
                    <li><i class="fas fa-chart-bar text-warning me-2"></i>Accuracy（准确率）</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- 快速开始 -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-rocket me-2"></i>
                    快速开始
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p class="mb-3">
                            <strong>准备工作：</strong>确保DATA目录下包含所有必需的数据文件：
                            primary.csv、alternate.csv、test_01.csv、test_02.xlsx
                        </p>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>提示：</strong>系统会自动验证数据完整性，并提供详细的处理进度和结果分析。
                            所有结果文件将以CSV/XLSX格式导出，便于后续分析。
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>注意：</strong>Sheet8是负样本测试集，匹配不到结果才是正常的。
                            系统会特别处理这种情况并提供专门的评估指标。
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <a href="{{ url_for('load_data') }}" class="btn btn-primary btn-lg mb-3">
                                <i class="fas fa-play me-2"></i>
                                立即开始
                            </a>
                            <br>
                            <small class="text-muted">点击开始数据加载流程</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 添加一些动画效果
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
</script>
{% endblock %}
