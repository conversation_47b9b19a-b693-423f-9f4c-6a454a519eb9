#!/usr/bin/env python3
"""
测试增强的首字母缩写匹配算法
"""

import pandas as pd
from modules.matcher import EntityMatcher

def test_initial_matching():
    print("=== 测试增强的首字母缩写匹配算法 ===")

    # 创建测试数据
    test_cases = [
        ("SHEL<PERSON>TE<PERSON>KO, <PERSON><PERSON><PERSON><PERSON>", "SHELESTENKO, Hennadiy O."),
        ("MALEKOUTI POUR, Hamidreza", "MALEKOUTI POUR, H."),
        ("TABATABAEI, <PERSON><PERSON><PERSON>", "TABATABAEI, S A Akbar"),
        ("CIRE, <PERSON>rsad Zafer", "CIRE, K. Z."),
        ("AGUILAR GARCIA, <PERSON>", "A GARCIA, Marvin R"),
        ("SMITH, <PERSON>", "SMITH, J. M."),
        ("<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "<PERSON><PERSON><PERSON><PERSON>, M E"),
        ("BROWN, <PERSON>", "BROWN, R J"),
    ]

    matcher = EntityMatcher()

    print("测试结果:")
    total_matches = 0
    for original, abbreviated in test_cases:
        score = matcher._simple_similarity(original, abbreviated)
        is_match = score >= 0.7
        if is_match:
            total_matches += 1

        print(f"原名: {original}")
        print(f"缩写: {abbreviated}")
        print(f"相似度: {score:.3f}")
        print(f"匹配: {'✓' if is_match else '✗'}")
        print("-" * 70)

    print(f"\n总结: {total_matches}/{len(test_cases)} 个测试用例匹配成功")
    print(f"匹配率: {total_matches/len(test_cases)*100:.1f}%")

if __name__ == "__main__":
    test_initial_matching()
