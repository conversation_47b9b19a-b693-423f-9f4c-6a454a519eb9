#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统功能测试脚本
System Functionality Test Script
"""

import pandas as pd
import numpy as np
import os
import sys
from modules.data_loader import DataLoader
from modules.preprocessor import TextPreprocessor
from modules.similarity_calculator import SimilarityCalculator
from modules.matcher import EntityMatcher
from modules.deduplicator import EntityDeduplicator
from modules.evaluator import PerformanceEvaluator

def test_data_loading():
    """测试数据加载功能"""
    print("=" * 50)
    print("测试数据加载功能")
    print("=" * 50)
    
    try:
        data_loader = DataLoader()
        
        # 检查数据文件是否存在
        data_files = [
            'DATA/primary.csv',
            'DATA/alternate.csv', 
            'DATA/test_01.csv',
            'DATA/test_02.xlsx'
        ]
        
        for file_path in data_files:
            if os.path.exists(file_path):
                print(f"✓ {file_path} 存在")
            else:
                print(f"✗ {file_path} 不存在")
                return False
        
        # 加载数据
        print("\n加载数据...")
        primary_df = data_loader.load_primary_data('DATA/primary.csv')
        print(f"Primary数据: {len(primary_df)} 条记录")
        
        alternate_df = data_loader.load_alternate_data('DATA/alternate.csv')
        print(f"Alternate数据: {len(alternate_df)} 条记录")
        
        test_01_df = data_loader.load_test_01_data('DATA/test_01.csv')
        print(f"Test_01数据: {len(test_01_df)} 条记录")
        
        test_02_sheets = data_loader.load_test_02_data('DATA/test_02.xlsx')
        print(f"Test_02数据: {len(test_02_sheets)} 个sheet")
        
        for sheet_name, sheet_df in test_02_sheets.items():
            if sheet_name != 'Desc':
                print(f"  - {sheet_name}: {len(sheet_df)} 条记录")
        
        print("✓ 数据加载测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据加载测试失败: {str(e)}")
        return False

def test_text_preprocessing():
    """测试文本预处理功能"""
    print("\n" + "=" * 50)
    print("测试文本预处理功能")
    print("=" * 50)
    
    try:
        preprocessor = TextPreprocessor()
        
        # 测试文本
        test_texts = [
            "AEROCARIBBEAN AIRLINES",
            "AERO-CARIBBEAN AIR LINES INC.",
            "aero caribbean airlines ltd",
            "AERO CARIBBEAN  AIRLINES"
        ]
        
        print("原始文本 -> 标准化文本")
        for text in test_texts:
            normalized = preprocessor.normalize_text(text)
            print(f"'{text}' -> '{normalized}'")
        
        # 测试不同sheet的预处理
        test_text = "AEROCARIBBEAN AIRLINES"
        print(f"\n测试文本: '{test_text}'")
        
        for sheet_name in ['Sheet1', 'Sheet2', 'Sheet3', 'Sheet4', 'Sheet5', 'Sheet6']:
            processed = preprocessor.preprocess_by_sheet_type(test_text, sheet_name)
            print(f"{sheet_name}: '{processed}'")
        
        print("✓ 文本预处理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 文本预处理测试失败: {str(e)}")
        return False

def test_similarity_calculation():
    """测试相似度计算功能"""
    print("\n" + "=" * 50)
    print("测试相似度计算功能")
    print("=" * 50)
    
    try:
        similarity_calc = SimilarityCalculator()
        
        # 测试文本对
        text_pairs = [
            ("AEROCARIBBEAN AIRLINES", "AERO CARIBBEAN AIRLINES"),
            ("AEROCARIBBEAN AIRLINES", "AEROCARIBEA NAIRLINES"),
            ("AEROCARIBBEAN AIRLINES", "COMPLETELY DIFFERENT TEXT"),
            ("HELLO WORLD", "HELLO WORLD")
        ]
        
        print("文本对相似度测试:")
        for text1, text2 in text_pairs:
            levenshtein = similarity_calc.levenshtein_similarity(text1, text2)
            jaro_winkler = similarity_calc.jaro_winkler_similarity(text1, text2)
            jaccard = similarity_calc.jaccard_token_similarity(text1, text2)
            combined = similarity_calc.combined_similarity(text1, text2)
            
            print(f"\n'{text1}' vs '{text2}':")
            print(f"  Levenshtein: {levenshtein:.3f}")
            print(f"  Jaro-Winkler: {jaro_winkler:.3f}")
            print(f"  Jaccard: {jaccard:.3f}")
            print(f"  Combined: {combined:.3f}")
        
        print("\n✓ 相似度计算测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 相似度计算测试失败: {str(e)}")
        return False

def test_small_matching():
    """测试小规模匹配功能"""
    print("\n" + "=" * 50)
    print("测试小规模匹配功能")
    print("=" * 50)
    
    try:
        # 创建小规模测试数据
        primary_data = {
            'ID': [1, 2, 3],
            'NAME': ['AEROCARIBBEAN AIRLINES', 'BRITISH AIRWAYS', 'CHINA AIRLINES'],
            'TYPE': ['C', 'C', 'C']
        }
        primary_df = pd.DataFrame(primary_data)
        
        test_data = {
            'ID': [1, 2, 3],
            'VARIANT': ['AERO CARIBBEAN AIRLINES', 'BRITISH AIR WAYS', 'CHINA AIR LINES']
        }
        test_df = pd.DataFrame(test_data)
        
        # 执行匹配
        matcher = EntityMatcher()
        matches = matcher.match_entities(
            test_df, 
            primary_df, 
            threshold=0.7, 
            method='combined',
            query_col='VARIANT',
            target_col='NAME'
        )
        
        print(f"匹配结果: {len(matches)} 个匹配")
        for _, match in matches.iterrows():
            print(f"  查询: '{match['query_name']}' -> 匹配: '{match['matched_name']}' (相似度: {match['similarity_score']:.3f})")
        
        print("✓ 小规模匹配测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 小规模匹配测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("实体匹配与去重系统 - 功能测试")
    print("=" * 60)
    
    # 检查当前目录
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    
    # 运行测试
    tests = [
        test_data_loading,
        test_text_preprocessing,
        test_similarity_calculation,
        test_small_matching
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统功能正常。")
        print("\n下一步:")
        print("1. 访问 http://127.0.0.1:5000 使用Web界面")
        print("2. 按照流程加载数据、执行去重和匹配")
        print("3. 查看评估结果和导出文件")
    else:
        print("⚠️ 部分测试失败，请检查系统配置。")
    
    return passed == total

if __name__ == "__main__":
    main()
