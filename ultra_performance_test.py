#!/usr/bin/env python3
"""
超高性能测试 - 测试终极优化版本
"""

import pandas as pd
import time
import logging
from modules.matcher import EntityMatcher

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def ultra_performance_test():
    """超高性能测试"""
    print("🚀 超高性能测试")
    print("=" * 50)
    
    # 加载数据
    print("📊 加载测试数据...")
    primary_df = pd.read_csv('DATA/primary.csv')
    test_01_df = pd.read_csv('DATA/test_01.csv')
    
    # 创建中等样本测试
    test_sample = test_01_df.head(200)  # 测试200条记录
    target_sample = primary_df.head(2000)  # 目标2000条记录
    
    print(f"测试样本: {len(test_sample)} 条记录")
    print(f"目标样本: {len(target_sample)} 条记录")
    
    # 初始化匹配器
    matcher = EntityMatcher()
    
    print("\n🔄 开始性能对比测试...")
    print("-" * 50)
    
    # 测试1: 原始算法
    print("🐌 测试原始算法...")
    start_time = time.time()
    
    original_matches = matcher.match_entities(
        test_sample,
        target_sample,
        threshold=0.7,
        method='combined',
        query_col='VARIANT',
        target_col='NAME'
    )
    
    original_time = time.time() - start_time
    print(f"✅ 原始算法: {len(original_matches)} 个匹配, {original_time:.2f}秒")
    
    # 测试2: 优化算法
    print("🚀 测试优化算法...")
    start_time = time.time()
    
    optimized_matches = matcher.fast_match_entities(
        test_sample,
        target_sample,
        threshold=0.7,
        method='combined',
        query_col='VARIANT',
        target_col='NAME'
    )
    
    optimized_time = time.time() - start_time
    print(f"✅ 优化算法: {len(optimized_matches)} 个匹配, {optimized_time:.2f}秒")
    
    # 测试3: 超高性能算法
    print("⚡ 测试超高性能算法...")
    start_time = time.time()
    
    ultra_matches = matcher.ultra_fast_match_entities(
        test_sample,
        target_sample,
        threshold=0.7,
        method='combined',
        query_col='VARIANT',
        target_col='NAME'
    )
    
    ultra_time = time.time() - start_time
    print(f"✅ 超高性能算法: {len(ultra_matches)} 个匹配, {ultra_time:.2f}秒")
    
    # 性能对比
    print("\n📈 性能对比结果")
    print("=" * 50)
    
    if optimized_time > 0 and ultra_time > 0:
        speedup_opt = original_time / optimized_time
        speedup_ultra = original_time / ultra_time
        ultra_vs_opt = optimized_time / ultra_time
        
        print(f"⚡ 优化算法提升: {speedup_opt:.2f}x")
        print(f"🚀 超高性能提升: {speedup_ultra:.2f}x")
        print(f"🔥 超高性能 vs 优化: {ultra_vs_opt:.2f}x")
        
        print(f"\n⏱️  时间对比:")
        print(f"原始算法: {original_time:.2f}秒")
        print(f"优化算法: {optimized_time:.2f}秒 (节省{((original_time-optimized_time)/original_time)*100:.1f}%)")
        print(f"超高性能: {ultra_time:.2f}秒 (节省{((original_time-ultra_time)/original_time)*100:.1f}%)")
        
        print(f"\n🎯 匹配质量:")
        print(f"原始算法: {len(original_matches)} 个匹配")
        print(f"优化算法: {len(optimized_matches)} 个匹配")
        print(f"超高性能: {len(ultra_matches)} 个匹配")
        
        # 处理速度
        orig_speed = len(test_sample) / original_time
        opt_speed = len(test_sample) / optimized_time
        ultra_speed = len(test_sample) / ultra_time
        
        print(f"\n🏃 处理速度:")
        print(f"原始算法: {orig_speed:.1f} 条/秒")
        print(f"优化算法: {opt_speed:.1f} 条/秒")
        print(f"超高性能: {ultra_speed:.1f} 条/秒")
        
        # 预估全量数据时间
        full_records = 16041
        full_target = 16041
        
        scale_factor = (full_records * full_target) / (len(test_sample) * len(target_sample))
        
        estimated_original = original_time * scale_factor
        estimated_optimized = optimized_time * scale_factor
        estimated_ultra = ultra_time * scale_factor
        
        print(f"\n🔮 全量数据预估 ({full_records} x {full_target}):")
        print(f"原始算法: {estimated_original/3600:.1f} 小时")
        print(f"优化算法: {estimated_optimized/60:.1f} 分钟")
        print(f"超高性能: {estimated_ultra/60:.1f} 分钟")
        
        if estimated_ultra < 3600:  # 小于1小时
            print(f"🎉 超高性能算法可在 {estimated_ultra/60:.1f} 分钟内完成全量匹配!")
        
    print("\n✅ 超高性能测试完成!")
    
    return {
        'original_time': original_time,
        'optimized_time': optimized_time,
        'ultra_time': ultra_time,
        'original_matches': len(original_matches),
        'optimized_matches': len(optimized_matches),
        'ultra_matches': len(ultra_matches)
    }

if __name__ == '__main__':
    try:
        results = ultra_performance_test()
        
        if results['ultra_time'] > 0:
            speedup = results['original_time'] / results['ultra_time']
            print(f"\n🎉 终极优化成功! 性能提升 {speedup:.2f}x")
            print(f"🚀 从 {results['original_time']:.2f}秒 优化到 {results['ultra_time']:.2f}秒")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        print(f"❌ 测试失败: {str(e)}")
