<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HW1 实体匹配与去重系统开发文档</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .data-structure {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .requirement {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .algorithm {
            background-color: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #17a2b8;
            margin: 10px 0;
        }
        .deliverable {
            background-color: #d4edda;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .code {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .warning {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HW1 实体匹配与去重系统开发文档</h1>
        
        <div class="section">
            <h2>📋 项目概述</h2>
            <p>本项目是一个实体匹配与去重系统，主要目标是开发算法来识别和匹配不同数据源中的相同实体，并评估算法的性能。</p>
            
            <div class="requirement">
                <h3>核心任务</h3>
                <ul>
                    <li>对primary.csv和alternate.csv进行去重处理</li>
                    <li>在test_01.csv和test_02.xlsx的8个sheet中进行实体匹配</li>
                    <li>计算precision、recall、F1-score等评估指标</li>
                    <li>分析算法对不同数据变换情况的检测能力</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📊 数据结构分析</h2>
            
            <h3>基础数据集</h3>
            <div class="data-structure">
                <h4>primary.csv (16,043条记录)</h4>
                <ul>
                    <li><strong>ID</strong>: 实体唯一标识符</li>
                    <li><strong>NAME</strong>: 实体名称（标准格式）</li>
                    <li><strong>TYPE</strong>: 实体类型（均为'C'）</li>
                </ul>
                
                <h4>alternate.csv (结构待分析)</h4>
                <ul>
                    <li>包含primary.csv中实体的变体形式</li>
                    <li>用于去重算法的训练和测试</li>
                </ul>
            </div>

            <h3>测试数据集</h3>
            <div class="data-structure">
                <h4>test_01.csv (16,043条记录)</h4>
                <ul>
                    <li><strong>ID</strong>: 实体标识符</li>
                    <li><strong>VARIANT</strong>: 实体名称的变体形式（包含拼写错误、特殊字符等）</li>
                </ul>
                
                <h4>test_02.xlsx (9个sheet)</h4>
                <table>
                    <tr>
                        <th>Sheet名称</th>
                        <th>数据变换类型</th>
                        <th>记录数</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>Desc</td>
                        <td>描述信息</td>
                        <td>7</td>
                        <td>各sheet的变换规则说明</td>
                    </tr>
                    <tr>
                        <td>Sheet1</td>
                        <td>移除特殊字符和空格</td>
                        <td>1,000</td>
                        <td>remove all special characters and white spaces</td>
                    </tr>
                    <tr>
                        <td>Sheet2</td>
                        <td>单词打乱+移除特殊字符</td>
                        <td>1,000</td>
                        <td>shuffle words then remove special characters</td>
                    </tr>
                    <tr>
                        <td>Sheet3</td>
                        <td>字符间插入空格</td>
                        <td>1,000</td>
                        <td>separate every character by white space</td>
                    </tr>
                    <tr>
                        <td>Sheet4</td>
                        <td>单词打乱+字符分离</td>
                        <td>1,000</td>
                        <td>shuffle words then separate characters</td>
                    </tr>
                    <tr>
                        <td>Sheet5</td>
                        <td>单词移除</td>
                        <td>1,000</td>
                        <td>word removal</td>
                    </tr>
                    <tr>
                        <td>Sheet6</td>
                        <td>单词截断</td>
                        <td>1,000</td>
                        <td>word truncation</td>
                    </tr>
                    <tr>
                        <td>Sheet7</td>
                        <td>待分析</td>
                        <td>1,000</td>
                        <td>需要进一步分析</td>
                    </tr>
                    <tr>
                        <td>Sheet8</td>
                        <td>负样本测试</td>
                        <td>1,000</td>
                        <td>⚠️ 无真实匹配，匹配不到才是正常的</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="section">
            <h2>🎯 核心需求分析</h2>
            
            <div class="requirement">
                <h3>1. 去重任务</h3>
                <ul>
                    <li>对primary.csv和alternate.csv进行实体去重</li>
                    <li>识别同一实体的不同表示形式</li>
                    <li>输出去重后的结果文件</li>
                </ul>
            </div>

            <div class="requirement">
                <h3>2. 匹配任务</h3>
                <ul>
                    <li>将test_01.csv中的变体与primary.csv进行匹配</li>
                    <li>将test_02.xlsx的8个sheet分别与primary.csv进行匹配</li>
                    <li>每个sheet需要单独计算评估指标</li>
                </ul>
            </div>

            <div class="warning">
                <h3>⚠️ 特别注意：Sheet8</h3>
                <p>Sheet8是负样本测试集，<strong>没有真实匹配</strong>。算法如果在Sheet8上匹配不到任何结果，说明算法性能良好，能够正确识别非匹配情况。</p>
            </div>
        </div>

        <div class="section">
            <h2>🔧 算法设计方案</h2>
            
            <div class="algorithm">
                <h3>1. 文本预处理模块</h3>
                <ul>
                    <li><strong>标准化</strong>: 统一大小写、移除多余空格</li>
                    <li><strong>特殊字符处理</strong>: 移除或标准化标点符号</li>
                    <li><strong>分词</strong>: 将实体名称分解为单词列表</li>
                    <li><strong>停用词过滤</strong>: 移除常见的无意义词汇</li>
                </ul>
            </div>

            <div class="algorithm">
                <h3>2. 相似度计算模块</h3>
                <ul>
                    <li><strong>字符串相似度</strong>: Levenshtein距离、Jaro-Winkler距离</li>
                    <li><strong>集合相似度</strong>: Jaccard系数、Dice系数</li>
                    <li><strong>序列相似度</strong>: 最长公共子序列(LCS)</li>
                    <li><strong>语义相似度</strong>: TF-IDF + 余弦相似度</li>
                </ul>
            </div>

            <div class="algorithm">
                <h3>3. 匹配策略</h3>
                <ul>
                    <li><strong>多层次匹配</strong>: 精确匹配 → 模糊匹配 → 语义匹配</li>
                    <li><strong>阈值优化</strong>: 针对不同变换类型调整匹配阈值</li>
                    <li><strong>集成方法</strong>: 组合多种相似度指标</li>
                    <li><strong>负样本检测</strong>: 特别针对Sheet8的反匹配机制</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📈 评估指标体系</h2>

            <div class="algorithm">
                <h3>核心指标</h3>
                <table>
                    <tr>
                        <th>指标</th>
                        <th>公式</th>
                        <th>含义</th>
                    </tr>
                    <tr>
                        <td>Precision (精确率)</td>
                        <td>TP / (TP + FP)</td>
                        <td>预测为正例中实际为正例的比例</td>
                    </tr>
                    <tr>
                        <td>Recall (召回率)</td>
                        <td>TP / (TP + FN)</td>
                        <td>实际正例中被正确预测的比例</td>
                    </tr>
                    <tr>
                        <td>F1-Score</td>
                        <td>2 * (Precision * Recall) / (Precision + Recall)</td>
                        <td>精确率和召回率的调和平均</td>
                    </tr>
                    <tr>
                        <td>Accuracy (准确率)</td>
                        <td>(TP + TN) / (TP + TN + FP + FN)</td>
                        <td>所有预测中正确预测的比例</td>
                    </tr>
                </table>
            </div>

            <div class="success">
                <h3>Sheet8特殊评估</h3>
                <p>对于Sheet8（负样本），评估重点是：</p>
                <ul>
                    <li><strong>False Positive Rate</strong>: 错误匹配率应该接近0</li>
                    <li><strong>Specificity</strong>: 正确识别负样本的能力</li>
                    <li>理想情况：算法在Sheet8上不产生任何匹配结果</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>💻 技术实现方案</h2>

            <div class="algorithm">
                <h3>开发环境</h3>
                <ul>
                    <li><strong>编程语言</strong>: Python 3.8+</li>
                    <li><strong>核心库</strong>: pandas, numpy, scikit-learn</li>
                    <li><strong>文本处理</strong>: nltk, fuzzywuzzy, python-Levenshtein</li>
                    <li><strong>数据处理</strong>: openpyxl (Excel文件处理)</li>
                </ul>
            </div>

            <div class="code">
                <h3>核心模块结构</h3>
                <pre>
entity_matching/
├── data_loader.py          # 数据加载模块
├── preprocessor.py         # 文本预处理模块
├── similarity_calculator.py # 相似度计算模块
├── matcher.py              # 匹配算法模块
├── evaluator.py            # 评估指标计算模块
├── deduplicator.py         # 去重模块
└── main.py                 # 主程序入口
                </pre>
            </div>
        </div>

        <div class="section">
            <h2>📋 开发计划</h2>

            <div class="algorithm">
                <h3>Phase 1: 数据分析与预处理 (1-2天)</h3>
                <ul>
                    <li>深入分析所有数据文件的结构和内容</li>
                    <li>理解各种数据变换的特点</li>
                    <li>设计通用的文本预处理流程</li>
                    <li>建立基准数据集</li>
                </ul>
            </div>

            <div class="algorithm">
                <h3>Phase 2: 算法开发 (2-3天)</h3>
                <ul>
                    <li>实现多种相似度计算方法</li>
                    <li>开发自适应匹配策略</li>
                    <li>针对不同sheet优化算法参数</li>
                    <li>实现去重算法</li>
                </ul>
            </div>

            <div class="algorithm">
                <h3>Phase 3: 评估与优化 (1-2天)</h3>
                <ul>
                    <li>在所有测试集上运行算法</li>
                    <li>计算详细的评估指标</li>
                    <li>分析算法在不同变换类型上的表现</li>
                    <li>优化算法参数</li>
                </ul>
            </div>

            <div class="algorithm">
                <h3>Phase 4: 结果输出与报告 (1天)</h3>
                <ul>
                    <li>生成所需的CSV/XLSX结果文件</li>
                    <li>编写详细的分析报告</li>
                    <li>总结算法优缺点</li>
                    <li>提出改进建议</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📤 交付物清单</h2>

            <div class="deliverable">
                <h3>必需交付物</h3>
                <ol>
                    <li><strong>去重结果文件</strong>
                        <ul>
                            <li>primary_deduped.csv - primary.csv去重结果</li>
                            <li>alternate_deduped.csv - alternate.csv去重结果</li>
                        </ul>
                    </li>
                    <li><strong>匹配结果文件</strong>
                        <ul>
                            <li>test_01_matches.csv - test_01匹配结果</li>
                            <li>test_02_sheet1_matches.csv - Sheet1匹配结果</li>
                            <li>test_02_sheet2_matches.csv - Sheet2匹配结果</li>
                            <li>... (每个sheet一个文件)</li>
                            <li>test_02_sheet8_matches.csv - Sheet8匹配结果</li>
                        </ul>
                    </li>
                    <li><strong>评估报告</strong>
                        <ul>
                            <li>evaluation_report.xlsx - 详细评估指标</li>
                            <li>analysis_summary.html - 分析总结报告</li>
                        </ul>
                    </li>
                </ol>
            </div>

            <div class="deliverable">
                <h3>结果文件格式规范</h3>
                <h4>匹配结果文件格式:</h4>
                <table>
                    <tr>
                        <th>列名</th>
                        <th>说明</th>
                        <th>示例</th>
                    </tr>
                    <tr>
                        <td>query_id</td>
                        <td>查询实体ID</td>
                        <td>36</td>
                    </tr>
                    <tr>
                        <td>query_name</td>
                        <td>查询实体名称</td>
                        <td>AEROCARIBEA NAIRLINES</td>
                    </tr>
                    <tr>
                        <td>matched_id</td>
                        <td>匹配到的实体ID</td>
                        <td>36</td>
                    </tr>
                    <tr>
                        <td>matched_name</td>
                        <td>匹配到的实体名称</td>
                        <td>AEROCARIBBEAN AIRLINES</td>
                    </tr>
                    <tr>
                        <td>similarity_score</td>
                        <td>相似度分数</td>
                        <td>0.95</td>
                    </tr>
                    <tr>
                        <td>match_method</td>
                        <td>匹配方法</td>
                        <td>fuzzy_string</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="section">
            <h2>⚠️ 关键注意事项</h2>

            <div class="warning">
                <h3>数据质量问题</h3>
                <ul>
                    <li>test_01.csv中包含大量拼写错误和特殊字符干扰</li>
                    <li>不同sheet的变换规则差异很大，需要针对性处理</li>
                    <li>Sheet8是负样本，算法需要能够正确拒绝匹配</li>
                </ul>
            </div>

            <div class="warning">
                <h3>算法挑战</h3>
                <ul>
                    <li>平衡精确率和召回率，避免过度匹配或匹配不足</li>
                    <li>处理单词顺序打乱的情况（Sheet2, Sheet4）</li>
                    <li>处理字符级别的变换（Sheet3, Sheet4）</li>
                    <li>处理信息缺失的情况（Sheet5, Sheet6）</li>
                </ul>
            </div>

            <div class="success">
                <h3>成功标准</h3>
                <ul>
                    <li>在大多数sheet上达到80%以上的F1-score</li>
                    <li>Sheet8的False Positive Rate低于5%</li>
                    <li>算法能够处理所有类型的数据变换</li>
                    <li>提供详细的性能分析和改进建议</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🔄 下一步行动</h2>

            <div class="algorithm">
                <ol>
                    <li><strong>立即开始</strong>: 详细分析alternate.csv和test_02.xlsx的所有sheet</li>
                    <li><strong>设计原型</strong>: 实现基础的匹配算法框架</li>
                    <li><strong>迭代优化</strong>: 在小数据集上测试和调优</li>
                    <li><strong>全面评估</strong>: 在所有测试集上运行完整评估</li>
                    <li><strong>结果分析</strong>: 深入分析算法在不同场景下的表现</li>
                </ol>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background-color: #e9ecef; border-radius: 10px;">
            <h3 style="color: #495057;">📞 项目支持</h3>
            <p style="color: #6c757d;">如需技术支持或有任何疑问，请及时沟通</p>
            <p style="color: #6c757d;"><em>文档生成时间: 2024年</em></p>
        </div>
    </div>
</body>
</html>
